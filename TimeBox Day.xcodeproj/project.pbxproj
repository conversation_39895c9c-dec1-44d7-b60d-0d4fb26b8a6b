// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		88BD0F7C2D77548D00BF1D9F /* Documentation.docc in Sources */ = {isa = PBXBuildFile; fileRef = 88BD0F7B2D77548D00BF1D9F /* Documentation.docc */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		882B2EF32D790E410085CAE7 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 88589CF62D77537700EBFCD5 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 88589CFD2D77537700EBFCD5;
			remoteInfo = "TimeBox Day";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		882B2EEF2D790E410085CAE7 /* TimeBox DayTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "TimeBox DayTests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
		88589CFE2D77537700EBFCD5 /* TimeBox Day.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "TimeBox Day.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		88BD0F7B2D77548D00BF1D9F /* Documentation.docc */ = {isa = PBXFileReference; lastKnownFileType = folder.documentationcatalog; path = Documentation.docc; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		88A7746D2D777CFD00C9E189 /* Exceptions for "TimeBox Day" folder in "TimeBox Day" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				App/TimeBoxModel.xcdatamodeld,
			);
			target = 88589CFD2D77537700EBFCD5 /* TimeBox Day */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedGroupBuildPhaseMembershipExceptionSet section */
		880A02FD2D77813B00655456 /* Exceptions for "TimeBox Day" folder in "Copy Bundle Resources" phase from "TimeBox Day" target */ = {
			isa = PBXFileSystemSynchronizedGroupBuildPhaseMembershipExceptionSet;
			buildPhase = 88589CFC2D77537700EBFCD5 /* Resources */;
			membershipExceptions = (
				App/TimeBoxModel.xcdatamodeld,
			);
		};
/* End PBXFileSystemSynchronizedGroupBuildPhaseMembershipExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		882B2EF02D790E410085CAE7 /* TimeBox DayTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = "TimeBox DayTests";
			sourceTree = "<group>";
		};
		88589D002D77537700EBFCD5 /* TimeBox Day */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				88A7746D2D777CFD00C9E189 /* Exceptions for "TimeBox Day" folder in "TimeBox Day" target */,
				880A02FD2D77813B00655456 /* Exceptions for "TimeBox Day" folder in "Copy Bundle Resources" phase from "TimeBox Day" target */,
			);
			path = "TimeBox Day";
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		882B2EEC2D790E410085CAE7 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		88589CFB2D77537700EBFCD5 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		88589CF52D77537700EBFCD5 = {
			isa = PBXGroup;
			children = (
				88589D002D77537700EBFCD5 /* TimeBox Day */,
				882B2EF02D790E410085CAE7 /* TimeBox DayTests */,
				88589CFF2D77537700EBFCD5 /* Products */,
				88BD0F7B2D77548D00BF1D9F /* Documentation.docc */,
			);
			sourceTree = "<group>";
		};
		88589CFF2D77537700EBFCD5 /* Products */ = {
			isa = PBXGroup;
			children = (
				88589CFE2D77537700EBFCD5 /* TimeBox Day.app */,
				882B2EEF2D790E410085CAE7 /* TimeBox DayTests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		882B2EEE2D790E410085CAE7 /* TimeBox DayTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 882B2EF52D790E410085CAE7 /* Build configuration list for PBXNativeTarget "TimeBox DayTests" */;
			buildPhases = (
				882B2EEB2D790E410085CAE7 /* Sources */,
				882B2EEC2D790E410085CAE7 /* Frameworks */,
				882B2EED2D790E410085CAE7 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				882B2EF42D790E410085CAE7 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				882B2EF02D790E410085CAE7 /* TimeBox DayTests */,
			);
			name = "TimeBox DayTests";
			packageProductDependencies = (
			);
			productName = "TimeBox DayTests";
			productReference = 882B2EEF2D790E410085CAE7 /* TimeBox DayTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		88589CFD2D77537700EBFCD5 /* TimeBox Day */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 88589D0C2D77537900EBFCD5 /* Build configuration list for PBXNativeTarget "TimeBox Day" */;
			buildPhases = (
				88589CFA2D77537700EBFCD5 /* Sources */,
				88589CFB2D77537700EBFCD5 /* Frameworks */,
				88589CFC2D77537700EBFCD5 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				88589D002D77537700EBFCD5 /* TimeBox Day */,
			);
			name = "TimeBox Day";
			packageProductDependencies = (
			);
			productName = "TimeBox Day";
			productReference = 88589CFE2D77537700EBFCD5 /* TimeBox Day.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		88589CF62D77537700EBFCD5 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					882B2EEE2D790E410085CAE7 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 88589CFD2D77537700EBFCD5;
					};
					88589CFD2D77537700EBFCD5 = {
						CreatedOnToolsVersion = 16.2;
					};
				};
			};
			buildConfigurationList = 88589CF92D77537700EBFCD5 /* Build configuration list for PBXProject "TimeBox Day" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 88589CF52D77537700EBFCD5;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 88589CFF2D77537700EBFCD5 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				88589CFD2D77537700EBFCD5 /* TimeBox Day */,
				882B2EEE2D790E410085CAE7 /* TimeBox DayTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		882B2EED2D790E410085CAE7 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		88589CFC2D77537700EBFCD5 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		882B2EEB2D790E410085CAE7 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		88589CFA2D77537700EBFCD5 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				88BD0F7C2D77548D00BF1D9F /* Documentation.docc in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		882B2EF42D790E410085CAE7 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 88589CFD2D77537700EBFCD5 /* TimeBox Day */;
			targetProxy = 882B2EF32D790E410085CAE7 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		882B2EF62D790E410085CAE7 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 8JTY5W49SA;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "name.aleksei.svezhevskii.TimeBox-DayTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/TimeBox Day.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/TimeBox Day";
			};
			name = Debug;
		};
		882B2EF72D790E410085CAE7 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 8JTY5W49SA;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "name.aleksei.svezhevskii.TimeBox-DayTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/TimeBox Day.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/TimeBox Day";
			};
			name = Release;
		};
		88589D0A2D77537900EBFCD5 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		88589D0B2D77537900EBFCD5 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		88589D0D2D77537900EBFCD5 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"TimeBox Day/Preview Content\"";
				DEVELOPMENT_TEAM = 8JTY5W49SA;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.productivity";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				IPHONEOS_DEPLOYMENT_TARGET = 16.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "name.aleksei.svezhevskii.TimeBox-Day";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		88589D0E2D77537900EBFCD5 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"TimeBox Day/Preview Content\"";
				DEVELOPMENT_TEAM = 8JTY5W49SA;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.productivity";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				IPHONEOS_DEPLOYMENT_TARGET = 16.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "name.aleksei.svezhevskii.TimeBox-Day";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		882B2EF52D790E410085CAE7 /* Build configuration list for PBXNativeTarget "TimeBox DayTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				882B2EF62D790E410085CAE7 /* Debug */,
				882B2EF72D790E410085CAE7 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		88589CF92D77537700EBFCD5 /* Build configuration list for PBXProject "TimeBox Day" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				88589D0A2D77537900EBFCD5 /* Debug */,
				88589D0B2D77537900EBFCD5 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		88589D0C2D77537900EBFCD5 /* Build configuration list for PBXNativeTarget "TimeBox Day" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				88589D0D2D77537900EBFCD5 /* Debug */,
				88589D0E2D77537900EBFCD5 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 88589CF62D77537700EBFCD5 /* Project object */;
}
