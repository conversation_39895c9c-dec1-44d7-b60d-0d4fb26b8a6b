import Foundation
import SwiftUI

struct TaskCategory: Identifiable, Equatable, Hashable {
    let id: UUID
    var name: String
    var color: Color
    
    init(id: UUID = UUID(), name: String, color: Color = .blue) {
        self.id = id
        self.name = name
        self.color = color
    }
    
    // MARK: - Hashable
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
}