import Foundation
import Combine
import SwiftUI

/// Расширения для оптимизации Combine-потоков
extension Publisher where Failure == Never {
    /// Создает разделяемый издатель для многократного использования
    func shareReplay() -> AnyPublisher<Output, Never> {
        self.share()
            .eraseToAnyPublisher()
    }
    
    /// Оптимизирует издатель, применяя базовые операторы
    func optimizeStream() -> AnyPublisher<Output, Never> where Output: Equatable {
        self.removeDuplicates()
            .share()
            .eraseToAnyPublisher()
    }
}