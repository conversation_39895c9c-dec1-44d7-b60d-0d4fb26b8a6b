import Foundation

struct Task: Identifiable, Equatable {
    
    static func == (lhs: Task, rhs: Task) -> Bool {
        return lhs.id == rhs.id
    }
    
    // Основные атрибуты задачи
    let id: UUID
    var title: String
    var description: String?
    var priority: TaskPriority
    var isCompleted: Bool
    var date: Date
    var category: TaskCategory?
    
    // Новые поля для временного планирования
    var startTime: Date?
    var duration: TimeInterval?
    
    // Вычисляемые свойства
    var isScheduled: Bool { startTime != nil && duration != nil }
    var endTime: Date? {
        guard let start = startTime, let duration = duration else { return nil }
        return start.addingTimeInterval(duration)
    }
    
    // Конструктор с новыми параметрами для планирования
    init(id: UUID = UUID(), 
         title: String, 
         description: String? = nil, 
         priority: TaskPriority = .tasks, 
         isCompleted: Bool = false, 
         date: Date = Date(), 
         category: TaskCategory? = nil,
         startTime: Date? = nil,
         duration: TimeInterval? = nil) {
        
        self.id = id
        self.title = title
        self.description = description
        self.priority = priority
        self.isCompleted = isCompleted
        self.date = date
        self.category = category
        self.startTime = startTime
        self.duration = duration
    }
}
