import Foundation
import SwiftUI

enum TaskPriority: Int, CaseIterable {
    case theOne = 0
    case tasks = 1
    
    var title: String {
        switch self {
        case .theOne: return "Progress Task"
        case .tasks: return "Tasks"
        }
    }
    
    var maxTasks: Int {
        switch self {
        case .theOne: return 1
        case .tasks: return .max
        }
    }
    
    var color: Color {
        switch self {
        case .theOne: return .red
        case .tasks: return .blue
        }
    }
}
