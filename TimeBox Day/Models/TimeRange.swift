import Foundation

/// Структура для работы с временными интервалами
struct TimeRange {
    let start: Date
    let duration: TimeInterval
    
    /// Время окончания интервала (вычисляемое свойство)
    var end: Date {
        return start.addingTimeInterval(duration)
    }
    
    /// Создает временной интервал с указанным началом и продолжительностью
    /// - Parameters:
    ///   - start: Время начала интервала
    ///   - duration: Продолжительность в секундах
    init(start: Date, duration: TimeInterval) {
        self.start = start
        self.duration = duration
    }
    
    /// Создает временной интервал с указанным началом и окончанием
    /// - Parameters:
    ///   - start: Время начала интервала
    ///   - end: Время окончания интервала
    init(start: Date, end: Date) {
        self.start = start
        self.duration = end.timeIntervalSince(start)
    }
    
    /// Проверяет, пересекается ли интервал с другим интервалом
    /// - Parameter other: Другой временной интервал
    /// - Returns: true, если интервалы пересекаются
    func overlaps(with other: TimeRange) -> Bool {
        return start < other.end && end > other.start
    }
    
    /// Проверяет, содержит ли интервал указанную дату
    /// - Parameter date: Дата для проверки
    /// - Returns: true, если дата попадает в интервал
    func contains(_ date: Date) -> Bool {
        return date >= start && date < end
    }
}