import SwiftUI

struct LoadingView: View {
    var message: String?
    
    var body: some View {
        VStack {
            ProgressView()
                .controlSize(.large)
            
            if let message = message {
                Text(message)
                    .font(.body)
                    .foregroundStyle(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
        .padding()
    }
}

#Preview {
    LoadingView(message: "Загрузка...")
}