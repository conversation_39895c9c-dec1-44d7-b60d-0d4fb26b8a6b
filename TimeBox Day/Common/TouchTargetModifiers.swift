import SwiftUI

// MARK: - Touch Target Modifiers
struct MinimumTouchTargetModifier: ViewModifier {
    @Environment(\.dynamicTouchTarget) private var touchTarget
    
    func body(content: Content) -> some View {
        content
            .frame(minWidth: touchTarget, minHeight: touchTarget)
    }
}

struct ExpandedTouchTargetModifier: ViewModifier {
    func body(content: Content) -> some View {
        content
            .contentShape(Rectangle())
    }
}

// MARK: - View Extensions
extension View {
    /// Ensures the view has a minimum touch target size as recommended by Apple's HIG
    func withMinimumTouchTarget() -> some View {
        modifier(MinimumTouchTargetModifier())
    }
    
    /// Expands the touch target area without changing the visual size of the element
    func withExpandedTouchTarget() -> some View {
        modifier(ExpandedTouchTargetModifier())
    }
}

// MARK: - Spring Animation Extension
extension View {
    /// Applies a standard spring animation to the given action
    /// - Parameter perform: The action to perform with animation
    func withSpringAnimation(_ perform: @escaping () -> Void) {
        withAnimation(
            .spring(
                response: AnimationConstants.springResponse,
                dampingFraction: AnimationConstants.springDamping
            )
        ) {
            perform()
        }
    }
}

// MARK: - Preview
#Preview {
    VStack {
        // Small button with minimum touch target
        Button(action: {}) {
            Image(systemName: "star")
                .imageScale(.small)
        }
        .withMinimumTouchTarget()
        .border(.red)
        
        // Small icon with expanded touch area
        Image(systemName: "heart")
            .imageScale(.small)
            .frame(width: 16, height: 16) // Using fixed size for preview
            .withExpandedTouchTarget()
            .border(.blue)
        
        Text("The red border shows the minimum touch target size.\nThe blue border shows the visual size, but the touch area extends beyond it.")
            .font(.caption)
            .multilineTextAlignment(.center)
            .padding()
    }
    .padding()
    .environment(\.dynamicTouchTarget, 44)
    .environment(\.dynamicStandardPadding, 16)
    .environment(\.dynamicSmallPadding, 8)
    .environment(\.dynamicSmallIcon, 16)
}