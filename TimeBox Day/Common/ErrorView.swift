import SwiftUI

struct ErrorView: View {
    let error: Error
    let retryAction: () -> Void
    
    var body: some View {
        VStack {
            Image(systemName: "exclamationmark.triangle.fill")
                .imageScale(.large)
                .font(.title)
                .foregroundStyle(.red)
            
            Text("Ошибка")
                .font(.headline)
            
            Text(error.localizedDescription)
                .font(.body)
                .multilineTextAlignment(.center)
            
            Button(action: retryAction) {
                Text("Повторить")
                    .fontWeight(.medium)
            }
        }
        .padding()
    }
}

#Preview {
    ErrorView(
        error: NSError(
            domain: "",
            code: -1,
            userInfo: [NSLocalizedDescriptionKey: "Тестовая ошибка"]
        ),
        retryAction: {}
    )
}