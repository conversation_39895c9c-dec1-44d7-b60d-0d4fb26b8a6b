import SwiftUI

// MARK: - Layout Constants
struct Layout {
    // Standard system sizes
    static let touchTarget: CGFloat = 44
    static let standardPadding: CGFloat = 16
    static let smallPadding: CGFloat = 8
    
    // Icons
    static let largeIcon: CGFloat = 38
    static let mediumIcon: CGFloat = 32
    static let smallIcon: CGFloat = 16
    
    // Calendar specific
    static let monthViewHeight: CGFloat = 240
    static let monthStripHeight: CGFloat = 60
    static let dragThreshold: CGFloat = 50
    static let monthViewHeightRatio: CGFloat = 0.8
}

// MARK: - Dynamic Layout Values
struct DynamicLayout: ViewModifier {
    @ScaledMetric(relativeTo: .body) private var touchTarget: CGFloat = 44
    @ScaledMetric(relativeTo: .body) private var standardPadding: CGFloat = 16
    @ScaledMetric(relativeTo: .body) private var smallPadding: CGFloat = 8
    @ScaledMetric(relativeTo: .body) private var largeIcon: CGFloat = 38
    @ScaledMetric(relativeTo: .body) private var mediumIcon: CGFloat = 32
    @ScaledMetric(relativeTo: .body) private var smallIcon: CGFloat = 16
    @ScaledMetric(relativeTo: .body) private var monthViewHeight: CGFloat = 240
    @ScaledMetric(relativeTo: .body) private var monthStripHeight: CGFloat = 60
    @ScaledMetric(relativeTo: .body) private var dragThreshold: CGFloat = 50
    
    func body(content: Content) -> some View {
        content
            .environment(\.dynamicTouchTarget, touchTarget)
            .environment(\.dynamicStandardPadding, standardPadding)
            .environment(\.dynamicSmallPadding, smallPadding)
            .environment(\.dynamicLargeIcon, largeIcon)
            .environment(\.dynamicMediumIcon, mediumIcon)
            .environment(\.dynamicSmallIcon, smallIcon)
            .environment(\.dynamicMonthViewHeight, monthViewHeight)
            .environment(\.dynamicMonthStripHeight, monthStripHeight)
            .environment(\.dynamicDragThreshold, dragThreshold)
    }
}

// MARK: - Animation Constants
struct AnimationConstants {
    static let scaleEffect: CGFloat = 0.9
    static let duration: CGFloat = 0.2
    static let springResponse: CGFloat = 0.3
    static let springDamping: CGFloat = 0.8
}

// MARK: - Priority Colors
struct PriorityColors {
    static let theOne = Color.red.opacity(0.9)
    static let tasks = Color.accentColor
    static let completed = Color.secondary.opacity(0.6)
}

// MARK: - Time Constants
struct TimeConstants {
    static let defaultDuration: TimeInterval = 30 * 60
    static let timeIntervals: [(String, TimeInterval)] = [
        ("15 минут", 15 * 60),
        ("30 минут", 30 * 60),
        ("45 минут", 45 * 60),
        ("1 час", 60 * 60),
        ("1.5 часа", 90 * 60),
        ("2 часа", 120 * 60)
    ]
    
    static func roundedStartTime(from date: Date = Date(), using dateTimeService: DateTimeServiceProtocol) -> Date {
        return dateTimeService.roundedTime(from: date)
    }
}

// MARK: - Calendar Style Modifier
struct CalendarStyleModifier: ViewModifier {
    @Environment(\.dynamicTouchTarget) private var touchTarget
    @Environment(\.dynamicMonthViewHeight) private var monthViewHeight
    @Environment(\.dynamicMonthStripHeight) private var monthStripHeight
    @Environment(\.dynamicDragThreshold) private var dragThreshold
    
    func body(content: Content) -> some View {
        content
            .environment(\.calendarDayItemSize, touchTarget)
            .environment(\.calendarWeekViewHeight, touchTarget)
            .environment(\.calendarMonthViewHeight, monthViewHeight)
            .environment(\.calendarMonthStripMinHeight, monthStripHeight)
            .environment(\.calendarDragThreshold, dragThreshold)
    }
}

// MARK: - Environment Keys
// Calendar Environment Keys
private struct CalendarDayItemSizeKey: EnvironmentKey {
    static let defaultValue: CGFloat = 44
}

private struct CalendarWeekViewHeightKey: EnvironmentKey {
    static let defaultValue: CGFloat = 44
}

private struct CalendarMonthViewHeightKey: EnvironmentKey {
    static let defaultValue: CGFloat = 240
}

private struct CalendarMonthStripMinHeightKey: EnvironmentKey {
    static let defaultValue: CGFloat = 60
}

private struct CalendarDragThresholdKey: EnvironmentKey {
    static let defaultValue: CGFloat = 50
}

// Dynamic Layout Environment Keys
private struct DynamicTouchTargetKey: EnvironmentKey {
    static let defaultValue: CGFloat = 44
}

private struct DynamicStandardPaddingKey: EnvironmentKey {
    static let defaultValue: CGFloat = 16
}

private struct DynamicSmallPaddingKey: EnvironmentKey {
    static let defaultValue: CGFloat = 8
}

private struct DynamicLargeIconKey: EnvironmentKey {
    static let defaultValue: CGFloat = 38
}

private struct DynamicMediumIconKey: EnvironmentKey {
    static let defaultValue: CGFloat = 32
}

private struct DynamicSmallIconKey: EnvironmentKey {
    static let defaultValue: CGFloat = 16
}

private struct DynamicMonthViewHeightKey: EnvironmentKey {
    static let defaultValue: CGFloat = 240
}

private struct DynamicMonthStripHeightKey: EnvironmentKey {
    static let defaultValue: CGFloat = 60
}

private struct DynamicDragThresholdKey: EnvironmentKey {
    static let defaultValue: CGFloat = 50
}

extension EnvironmentValues {
    // Calendar environment values
    var calendarDayItemSize: CGFloat {
        get { self[CalendarDayItemSizeKey.self] }
        set { self[CalendarDayItemSizeKey.self] = newValue }
    }
    var calendarWeekViewHeight: CGFloat {
        get { self[CalendarWeekViewHeightKey.self] }
        set { self[CalendarWeekViewHeightKey.self] = newValue }
    }
    var calendarMonthViewHeight: CGFloat {
        get { self[CalendarMonthViewHeightKey.self] }
        set { self[CalendarMonthViewHeightKey.self] = newValue }
    }
    var calendarMonthStripMinHeight: CGFloat {
        get { self[CalendarMonthStripMinHeightKey.self] }
        set { self[CalendarMonthStripMinHeightKey.self] = newValue }
    }
    var calendarDragThreshold: CGFloat {
        get { self[CalendarDragThresholdKey.self] }
        set { self[CalendarDragThresholdKey.self] = newValue }
    }
    
    // Dynamic layout environment values
    var dynamicTouchTarget: CGFloat {
        get { self[DynamicTouchTargetKey.self] }
        set { self[DynamicTouchTargetKey.self] = newValue }
    }
    var dynamicStandardPadding: CGFloat {
        get { self[DynamicStandardPaddingKey.self] }
        set { self[DynamicStandardPaddingKey.self] = newValue }
    }
    var dynamicSmallPadding: CGFloat {
        get { self[DynamicSmallPaddingKey.self] }
        set { self[DynamicSmallPaddingKey.self] = newValue }
    }
    var dynamicLargeIcon: CGFloat {
        get { self[DynamicLargeIconKey.self] }
        set { self[DynamicLargeIconKey.self] = newValue }
    }
    var dynamicMediumIcon: CGFloat {
        get { self[DynamicMediumIconKey.self] }
        set { self[DynamicMediumIconKey.self] = newValue }
    }
    var dynamicSmallIcon: CGFloat {
        get { self[DynamicSmallIconKey.self] }
        set { self[DynamicSmallIconKey.self] = newValue }
    }
    var dynamicMonthViewHeight: CGFloat {
        get { self[DynamicMonthViewHeightKey.self] }
        set { self[DynamicMonthViewHeightKey.self] = newValue }
    }
    var dynamicMonthStripHeight: CGFloat {
        get { self[DynamicMonthStripHeightKey.self] }
        set { self[DynamicMonthStripHeightKey.self] = newValue }
    }
    var dynamicDragThreshold: CGFloat {
        get { self[DynamicDragThresholdKey.self] }
        set { self[DynamicDragThresholdKey.self] = newValue }
    }
}

extension View {
    func withCalendarStyle() -> some View {
        modifier(CalendarStyleModifier())
    }
}