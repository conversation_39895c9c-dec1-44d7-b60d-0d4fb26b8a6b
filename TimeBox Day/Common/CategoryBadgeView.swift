import SwiftUI

struct CategoryBadgeView: View {
    let title: String
    let color: Color
    
    var body: some View {
        Text(title)
            .font(.footnote)
            .foregroundColor(.white)
            .lineLimit(1)
            .padding(.horizontal, Layout.smallPadding)
            .padding(.vertical, Layout.smallPadding / 4)
            .background(
                Capsule()
                    .fill(color)
            )
    }
}

#Preview {
    CategoryBadgeView(
        title: "Работа",
        color: .blue
    )
}