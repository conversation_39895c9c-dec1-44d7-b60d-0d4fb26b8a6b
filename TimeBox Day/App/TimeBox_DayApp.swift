import SwiftUI
import CoreData
import Combine

@main
struct TimeBoxDayApp: App {
    @StateObject private var container = DIContainer.shared
    @StateObject private var appState: AppState
    @StateObject private var settings = AppSettings.shared
    private var cancellableStorage = CancellableStorage()
    
    init() {
        let appState = AppState(container: DIContainer.shared)
        _appState = StateObject(wrappedValue: appState)
    }
    
    var body: some Scene {
        WindowGroup {
            MainTabView()
                .environmentObject(container)
                .environmentObject(appState)
                .preferredColorScheme(colorScheme)
        }
    }
    
    private var colorScheme: ColorScheme? {
        switch settings.selectedTheme {
        case .light:
            return .light
        case .dark:
            return .dark
        case .system:
            return nil // nil означает использование системной темы
        }
    }
}

private class CancellableStorage {
    var cancellables = Set<AnyCancellable>()
}
