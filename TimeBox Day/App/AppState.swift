import SwiftUI
import Combine

final class AppState: ObservableObject {
    // MARK: - Published Properties
    @Published var selectedDate: Date = Date()
    @Published var isEditing: Bool = false
    @Published private(set) var isShowingPastDate: Bool = false
    
    private let container: DIContainer
    private var cancellables = Set<AnyCancellable>()
    
    init(container: DIContainer = .shared) {
        self.container = container
        setupSubscriptions()
    }
    
    private func setupSubscriptions() {
        // Обновляем только isShowingPastDate при изменении selectedDate
        $selectedDate
            .map { self.container.dateTimeService.isDateInPast($0) }
            .assign(to: &$isShowingPastDate)
    }
    
    // MARK: - Date Management Methods
    
    func selectDate(_ date: Date) {
        // Убеждаемся, что обновление происходит на главном потоке
        DispatchQueue.main.async { [weak self] in
            self?.selectedDate = date
        }
    }
    
    func moveToNextDay() {
        if let nextDay = container.dateTimeService.date(byAdjusting: .day, value: 1, of: selectedDate) {
            selectDate(nextDay)
        }
    }
    
    func moveToPreviousDay() {
        if let previousDay = container.dateTimeService.date(byAdjusting: .day, value: -1, of: selectedDate) {
            selectDate(previousDay)
        }
    }
    
    func moveToToday() {
        selectDate(container.dateTimeService.getTodayDate())
    }
}
