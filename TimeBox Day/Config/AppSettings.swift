//
//  AppSettings.swift
//  TimeBox Day
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 05.03.2025.
//

import SwiftUI
import Combine

final class AppSettings: ObservableObject {
    // MARK: - Shared Instance
    static let shared = AppSettings()
    
    // MARK: - General Settings
    
    /// Selected app theme
    @Published var selectedTheme: Theme {
        didSet {
            UserDefaults.standard.set(selectedTheme.rawValue, forKey: "selectedTheme")
        }
    }
    
    // MARK: - Notification Settings
    
    /// Whether notifications are enabled
    @Published var notificationsEnabled: Bool {
        didSet {
            UserDefaults.standard.set(notificationsEnabled, forKey: "notificationsEnabled")
        }
    }
    
    // MARK: - Calendar Integration
    
    /// Whether system calendar integration is enabled
    @Published var systemCalendarEnabled: Bool {
        didSet {
            UserDefaults.standard.set(systemCalendarEnabled, forKey: "systemCalendarEnabled")
        }
    }
    
    /// Whether Google Calendar integration is enabled
    @Published var googleCalendarEnabled: Bool {
        didSet {
            UserDefaults.standard.set(googleCalendarEnabled, forKey: "googleCalendarEnabled")
        }
    }
    
    // MARK: - UI Settings
    
    /// Whether haptic feedback is enabled
    @Published var hapticFeedbackEnabled: Bool {
        didSet {
            UserDefaults.standard.set(hapticFeedbackEnabled, forKey: "hapticFeedbackEnabled")
        }
    }
    
    // MARK: - Theme Options
    enum Theme: String, CaseIterable {
        case system
        case light
        case dark
    }
    
    // MARK: - Initialization
    private init() {
        // Load theme
        let themeString = UserDefaults.standard.string(forKey: "selectedTheme") ?? Theme.system.rawValue
        self.selectedTheme = Theme(rawValue: themeString) ?? .system
        
        // Load notifications setting
        self.notificationsEnabled = UserDefaults.standard.bool(forKey: "notificationsEnabled")
        
        // Load calendar integration settings
        self.systemCalendarEnabled = UserDefaults.standard.bool(forKey: "systemCalendarEnabled")
        self.googleCalendarEnabled = UserDefaults.standard.bool(forKey: "googleCalendarEnabled")
        
        // Load UI settings
        self.hapticFeedbackEnabled = UserDefaults.standard.bool(forKey: "hapticFeedbackEnabled")
    }
}

