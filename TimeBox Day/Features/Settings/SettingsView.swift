import SwiftUI

struct SettingsView: View {
    @EnvironmentObject private var appState: AppState
    @StateObject private var settings = AppSettings.shared
    
    var body: some View {
        Form {
            Section {
                ThemeSelector(selectedTheme: $settings.selectedTheme)
                    .withMinimumTouchTarget()
            } header: {
                Text("Appearance")
                    .padding(.horizontal, 0)
            }
            
            Section {
                Toggle("Enable Notifications", isOn: $settings.notificationsEnabled)
                    .withMinimumTouchTarget()
            } header: {
                Text("Notifications")
                    .padding(.horizontal, 0)
            }
            
            Section {
                Toggle("System Calendar", isOn: $settings.systemCalendarEnabled)
                    .withMinimumTouchTarget()
                Toggle("Google Calendar", isOn: $settings.googleCalendarEnabled)
                    .withMinimumTouchTarget()
            } header: {
                Text("Calendar Integration")
                    .padding(.horizontal, 0)
            }
        }
        .navigationTitle("Settings")
    }
}

private struct ThemeSelector: View {
    @Binding var selectedTheme: AppSettings.Theme
    
    var body: some View {
        Picker("Theme", selection: $selectedTheme) {
            ForEach(AppSettings.Theme.allCases, id: \.self) { theme in
                Text(theme.rawValue.capitalized).tag(theme)
            }
        }
        .pickerStyle(.navigationLink)
    }
}

#Preview {
    NavigationStack {
        SettingsView()
            .environmentObject(AppState(container: DIContainer.shared))
    }
}