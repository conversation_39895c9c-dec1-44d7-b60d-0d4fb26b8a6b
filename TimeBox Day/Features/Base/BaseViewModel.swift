import Foundation
import Combine
class BaseViewModel: ObservableObject {
    // MARK: - Properties
    
    let container: DIContainer
    var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    
    init(container: DIContainer) {
        self.container = container
        setupBindings()
    }
    
    // MARK: - Setup
    
    /// Метод для настройки привязок данных. Переопределяется в подклассах
    func setupBindings() {}
    
    deinit {
        cancellables.forEach { $0.cancel() }
        cancellables.removeAll()
    }
}