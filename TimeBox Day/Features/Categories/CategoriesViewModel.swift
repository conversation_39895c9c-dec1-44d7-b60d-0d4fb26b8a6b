import Foundation
import Combine
import SwiftUI

class CategoriesViewModel: BaseViewModel {
    // MARK: - Published Properties
    @Published private(set) var categories: [TaskCategory] = []
    @Published private(set) var isLoading = false
    @Published private(set) var error: Error?
    @Published var availableColors: [Color] = [
        .red, .orange, .yellow, .green,
        .blue, .indigo, .purple, .pink
    ]
    
    // MARK: - Initialization
    override init(container: DIContainer = .shared) {
        super.init(container: container)
    }
    
    // MARK: - Private Methods
    override func setupBindings() {
        super.setupBindings()
        
        // Только настраиваем привязки к данным, без их загрузки
        container.dataManager.$categories
            .assign(to: \.categories, on: self)
            .store(in: &cancellables)
        
        container.dataManager.$isLoading
            .assign(to: \.isLoading, on: self)
            .store(in: &cancellables)
        
        container.dataManager.$error
            .assign(to: \.error, on: self)
            .store(in: &cancellables)
    }
    
    // MARK: - Public Methods
    
    /// Метод, который следует вызывать из .onAppear представления для безопасной загрузки данных
    func onViewAppeared() {
        // Убедимся, что метод вызывается вне цикла обновления представления
        DispatchQueue.main.async { [weak self] in
            self?.fetchCategories()
        }
    }
    
    func fetchCategories() {
        // Вызываем метод dataManager напрямую
        container.dataManager.fetchAllCategories()
    }
    
    func deleteCategory(withID id: UUID) -> Bool {
        container.dataManager.deleteCategory(withID: id)
    }
}