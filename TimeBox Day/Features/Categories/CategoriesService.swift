import Foundation
import SwiftUI

protocol CategoryServiceProtocol {
    var categories: [TaskCategory] { get }
    func validateCategory(_ category: TaskCategory, existingCategories: [TaskCategory]) -> Bool
    func updateCategories(_ categories: [TaskCategory])
    func addCategory(_ category: TaskCategory) -> Bool
    /// Updates an existing category
    /// - Parameter category: The category to update
    /// - Returns: True if the update was successful, false otherwise
    func updateCategory(_ category: TaskCategory) -> Bool
}

class CategoryService: CategoryServiceProtocol {
    private(set) var categories: [TaskCategory] = []
    
    func validateCategory(_ category: TaskCategory, existingCategories: [TaskCategory]) -> <PERSON>ol {
        // Проверяем уникальность имени категории
        let nameExists = existingCategories
            .filter { $0.id != category.id }
            .contains { $0.name.lowercased() == category.name.lowercased() }
        
        return !nameExists
    }
    
    func updateCategories(_ categories: [TaskCategory]) {
        self.categories = categories
    }
    
    func addCategory(_ category: TaskCategory) -> Bool {
        if validateCategory(category, existingCategories: categories) {
            categories.append(category)
            return true
        }
        return false
    }
    
    func updateCategory(_ category: TaskCategory) -> Bool {
        if validateCategory(category, existingCategories: categories) {
            if let index = categories.firstIndex(where: { $0.id == category.id }) {
                categories[index] = category
                return true
            }
        }
        return false
    }
}