import SwiftUI

struct CategoriesView: View {
    @EnvironmentObject private var appState: AppState
    @EnvironmentObject private var coordinator: TabCoordinator
    @StateObject private var viewModel: CategoriesViewModel
    @State private var showingDeleteError = false
    
    init() {
        _viewModel = StateObject(wrappedValue: CategoriesViewModel(container: DIContainer.shared))
    }
    
    var body: some View {
        List {
            ForEach(viewModel.categories) { category in
                CategoryBadgeView(title: category.name, color: category.color)
                    .padding(.horizontal, Layout.smallPadding)
                    .padding(.vertical, Layout.smallPadding)
                    .contentShape(Rectangle())
                    .onTapGesture {
                        coordinator.navigate(to: .categoryDetail(category))
                    }
                    .contextMenu {
                        Button {
                            coordinator.navigate(to: .editCategory(category))
                        } label: {
                            Label("Редактировать", systemImage: "pencil")
                        }
                    }
            }
            .onDelete(perform: deleteCategory)
        }
        .listStyle(.insetGrouped)
        .overlay {
            if viewModel.categories.isEmpty {
                EmptyStateView(
                    icon: "folder",
                    title: "Нет категорий",
                    message: "Добавьте категории для организации задач"
                )
            }
        }
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: {
                    coordinator.navigate(to: .addCategory)
                }) {
                    Image(systemName: "plus")
                }
            }
        }
        .alert("Ошибка удаления", isPresented: $showingDeleteError) {
            Button("OK", role: .cancel) { }
        } message: {
            Text("Не удалось удалить категорию. Попробуйте еще раз.")
        }
        .onAppear {
            // Используем безопасный метод загрузки данных
            viewModel.onViewAppeared()
        }
    }
    
    private func deleteCategory(at offsets: IndexSet) {
        let categories = viewModel.categories
        
        for index in offsets {
            if index < categories.count {
                if !viewModel.deleteCategory(withID: categories[index].id) {
                    showingDeleteError = true
                }
            }
        }
    }
}

struct ColorCircleView: View {
    let color: Color
    let isSelected: Bool
    let onTap: () -> Void
    
    @ScaledMetric private var circleSize: CGFloat = Layout.mediumIcon
    @ScaledMetric private var borderWidth: CGFloat = 2
    
    var body: some View {
        Circle()
            .fill(color)
            .frame(width: circleSize, height: circleSize)
            .overlay {
                Circle()
                    .strokeBorder(isSelected ? Color.primary : Color.clear, lineWidth: borderWidth)
            }
            .withMinimumTouchTarget()
            .onTapGesture(perform: onTap)
    }
}

struct AddCategoryView: View {
    @Environment(\.dismiss) private var dismiss
    private let container = DIContainer.shared
    var onCategorySaved: () -> Void
    @StateObject private var viewModel: CategoriesViewModel
    
    // Поддержка редактирования существующей категории
    var editingCategory: TaskCategory?
    
    @State private var name = ""
    @State private var selectedColor: Color = .blue  // Default color
    
    init(editingCategory: TaskCategory? = nil, onCategorySaved: @escaping () -> Void) {
        self.editingCategory = editingCategory
        self.onCategorySaved = onCategorySaved
        _viewModel = StateObject(wrappedValue: CategoriesViewModel(container: DIContainer.shared))
    }
    
    var body: some View {
        NavigationStack {
            Form {
                Section(header: Text("Details")) {
                    TextField("Category Name", text: $name)
                        .textInputAutocapitalization(.sentences)
                        .font(.body)
                }
                
                Section(header: Text("Color")) {
                    ColorSelectionGrid(
                        availableColors: viewModel.availableColors,
                        selectedColor: $selectedColor
                    )
                }
            }
            .navigationTitle(editingCategory == nil ? "New Category" : "Edit Category")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .font(.body)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        saveCategory()
                    }
                    .fontWeight(.medium)
                    .disabled(name.isEmpty)
                }
            }
            .onAppear {
                if let category = editingCategory {
                    // Инициализируем форму данными существующей категории
                    name = category.name
                    selectedColor = category.color
                }
            }
        }
    }
    
    private func saveCategory() {
        let success: Bool
        
        if let existingCategory = editingCategory {
            // Редактирование существующей категории
            var updatedCategory = existingCategory
            updatedCategory.name = name
            updatedCategory.color = selectedColor
            
            success = container.categoryService.updateCategory(updatedCategory)
        } else {
            // Создание новой категории
            let newCategory = TaskCategory(
                name: name,
                color: selectedColor
            )
            
            success = container.categoryService.addCategory(newCategory)
        }
        
        if success {
            onCategorySaved()
            dismiss()
        }
    }
}

struct ColorSelectionGrid: View {
    let availableColors: [Color]
    @Binding var selectedColor: Color
    
    @ScaledMetric private var gridSpacing: CGFloat = Layout.smallPadding
    @ScaledMetric private var verticalPadding: CGFloat = Layout.smallPadding
    private let columns = [GridItem(.adaptive(minimum: Layout.mediumIcon))]
    
    var body: some View {
        LazyVGrid(columns: columns, spacing: gridSpacing) {
            ForEach(availableColors, id: \.self) { color in
                ColorCircleView(
                    color: color,
                    isSelected: color == selectedColor,
                    onTap: { selectedColor = color }
                )
            }
        }
        .padding(.vertical, verticalPadding)
    }
}

#Preview {
    ColorSelectionGrid(
        availableColors: [.red, .blue, .green],
        selectedColor: .constant(.blue)
    )
    .environmentObject(DIContainer.shared)
}