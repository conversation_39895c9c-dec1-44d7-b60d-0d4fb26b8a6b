import SwiftUI

struct CalendarView: View {
    // MARK: - Properties
    @StateObject private var viewModel: CalendarViewModel
    @EnvironmentObject private var appState: AppState
    @EnvironmentObject private var container: DIContainer
    @Environment(\.calendarDragThreshold) private var dragThreshold
    
    // MARK: - Initialization
    init(selectedDate: Date = Date()) {
        _viewModel = StateObject(wrappedValue: CalendarViewModel(container: DIContainer.shared))
    }
    
    // MARK: - Body
    var body: some View {
        VStack(spacing: 0) {
            // Заголовок календаря с кнопками
            calendarHeader
            
            // Заголовки дней недели
            weekdayHeaders
            
            // Основное содержимое календаря: недельный или месячный вид
            if viewModel.isMonthViewActive {
                monthView
            } else {
                weekView
            }
        }
        .background(Color(uiColor: .systemBackground))
        .onAppear {
            // При появлении представления используем AppState из окружения
            viewModel.setAppState(appState)
        }
        .withCalendarStyle()
    }
    
    // MARK: - Calendar Header
    private var calendarHeader: some View {
        HStack {
            // Кнопка "Сегодня"
            if viewModel.shouldShowTodayButton() {
                Button("Today") {
                    viewModel.goToToday()
                }
                .font(.headline)
                .foregroundColor(.accentColor)
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(Color.accentColor.opacity(0.1))
                .cornerRadius(8)
            }
            
            Spacer()
            
            // Заголовок с месяцем/годом и кнопкой переключения режима
            Button(action: viewModel.toggleCalendarView) {
                HStack {
                    Text(viewModel.monthYearString())
                        .font(.headline)
                    Image(systemName: "chevron.down")
                        .imageScale(.small)
                        .rotationEffect(.degrees(viewModel.isMonthViewActive ? 180 : 0))
                }
                .contentShape(Rectangle())
            }
        }
        .withMinimumTouchTarget()
        .frame(maxWidth: .infinity)
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
    }
    
    // MARK: - Weekday Headers
    private var weekdayHeaders: some View {
        HStack(spacing: 0) {
            ForEach(getWeekdaySymbols(), id: \.id) { weekday in
                Text(weekday.symbol)
                    .font(.caption2)
                    .foregroundStyle(.secondary)
                    .frame(maxWidth: .infinity)
            }
        }
        .frame(height: 25)
        .background {
            RoundedRectangle(cornerRadius: 4)
                .fill(Color(.systemGray6))
                .opacity(0.7)
        }
        .padding(.horizontal, 16)
    }
    
    // MARK: - Week View
    private var weekView: some View {
        WeekGridView(
            dates: viewModel.weekDates,
            selectedDate: appState.selectedDate,
            hasTask: viewModel.hasTasksForDate,
            onDateSelected: { date in
                appState.selectDate(date)
            }
        )
        .gesture(
            DragGesture()
                .onEnded { value in
                    if value.translation.width > dragThreshold {
                        viewModel.moveToPreviousWeek()
                    } else if value.translation.width < -dragThreshold {
                        viewModel.moveToNextWeek()
                    }
                }
        )
        .animation(.spring(
            response: AnimationConstants.springResponse,
            dampingFraction: AnimationConstants.springDamping
        ), value: viewModel.weekDates)
    }
    
    // MARK: - Month View
    @Environment(\.calendarMonthViewHeight) private var monthViewHeight
    private var monthView: some View {
        MonthGridView(
            date: viewModel.currentDisplayedDate,
            selectedDate: appState.selectedDate,
            hasTask: viewModel.hasTasksForDate,
            onDateSelected: { date in
                appState.selectDate(date)
                viewModel.updateDisplayedMonth(date)
                viewModel.updateWeekDates(for: date)
                viewModel.isMonthViewActive = false
            }
        )
        .highPriorityGesture(
            DragGesture()
                .onEnded { value in
                    let verticalTranslation = abs(value.translation.height)
                    let horizontalTranslation = abs(value.translation.width)
                    
                    if verticalTranslation > horizontalTranslation && verticalTranslation > dragThreshold {
                        withAnimation(.spring(
                            response: AnimationConstants.springResponse,
                            dampingFraction: AnimationConstants.springDamping
                        )) {
                            if value.translation.height > 0 {
                                viewModel.moveToPreviousMonth()
                            } else {
                                viewModel.moveToNextMonth()
                            }
                        }
                    }
                }
        )
        .frame(height: monthViewHeight)
        .clipped()
    }
    
    // MARK: - Helper Methods
    private func getWeekdaySymbols() -> [(id: String, symbol: String)] {
        var symbols = container.dateTimeService.getShortWeekdaySymbols()
        
        // Переупорядочиваем символы, чтобы они соответствовали первому дню недели
        let firstWeekday = CalendarConstants.firstWeekday
        
        if (firstWeekday > 1) {
            let symbolsToMove = symbols.prefix(firstWeekday - 1)
            symbols.removeFirst(firstWeekday - 1)
            symbols.append(contentsOf: symbolsToMove)
        }
        
        // Добавляем порядковый номер к каждому символу для уникальности
        return symbols.enumerated().map { (index, symbol) in 
            (id: "weekday_\(index)", symbol: symbol)
        }
    }
}

// MARK: - Недельная сетка
private struct WeekGridView: View {
    let dates: [Date]
    let selectedDate: Date
    let hasTask: (Date) -> Bool
    let onDateSelected: (Date) -> Void
    @EnvironmentObject private var container: DIContainer
    @Environment(\.calendarWeekViewHeight) private var weekViewHeight
    
    var body: some View {
        HStack(spacing: 0) {
            ForEach(dates, id: \.self) { date in
                CalendarDayCell(
                    date: date,
                    isSelected: container.dateTimeService.areDatesInSameDay(date, selectedDate),
                    hasTask: hasTask(date),
                    isInCurrentMonth: true
                )
                .frame(maxWidth: .infinity)
                .contentShape(Rectangle())
                .onTapGesture {
                    onDateSelected(date)
                }
            }
        }
        .padding(.horizontal, 16)
        .frame(height: weekViewHeight)
    }
}

// MARK: - Месячная сетка
private struct MonthGridView: View {
    let date: Date
    let selectedDate: Date
    let hasTask: (Date) -> Bool
    let onDateSelected: (Date) -> Void
    @EnvironmentObject private var container: DIContainer
    @Environment(\.calendarMonthViewHeight) private var monthViewHeight
    
    private var calendar: Calendar {
        container.dateTimeService.calendar
    }
    
    var body: some View {
        VStack(spacing: 8) {
            ForEach(container.dateTimeService.getMonthWeeks(for: date), id: \.self) { week in
                HStack(spacing: 0) {
                    ForEach(week, id: \.self) { date in
                        CalendarDayCell(
                            date: date,
                            isSelected: container.dateTimeService.areDatesInSameDay(date, selectedDate),
                            hasTask: hasTask(date),
                            isInCurrentMonth: calendar.isDate(date, equalTo: self.date, toGranularity: .month)
                        )
                        .frame(maxWidth: .infinity)
                        .contentShape(Rectangle())
                        .onTapGesture {
                            onDateSelected(date)
                        }
                    }
                }
            }
        }
        .padding(.horizontal, 16)
        .frame(height: monthViewHeight)
        .clipped()
        .animation(.spring(
            response: AnimationConstants.springResponse,
            dampingFraction: AnimationConstants.springDamping
        ), value: date)
    }
}

// MARK: - Ячейка дня календаря
private struct CalendarDayCell: View {
    let date: Date
    let isSelected: Bool
    let hasTask: Bool
    let isInCurrentMonth: Bool
    @EnvironmentObject private var container: DIContainer
    @Environment(\.calendarDayItemSize) private var dayItemSize
    
    private var calendar: Calendar {
        container.dateTimeService.calendar
    }
    
    private var isToday: Bool {
        calendar.isDateInToday(date)
    }
    
    private var isPast: Bool {
        container.dateTimeService.isDateInPast(date)
    }
    
    var body: some View {
        ZStack(alignment: .center) {
            Text(dayString)
                .font(isSelected ? .headline : .body)
                .frame(minWidth: dayItemSize, minHeight: dayItemSize)
                .background {
                    if isSelected {
                        Circle().fill(Color.accentColor)
                    } else if isToday {
                        Circle().stroke(Color.accentColor, lineWidth: 1)
                    }
                }
                .foregroundStyle(foregroundColor)
            
            if hasTask && !isSelected {
                Circle()
                    .fill(Color.accentColor)
                    .frame(maxWidth: 6, maxHeight: 6, alignment: .center)
                    .scaleEffect(isInCurrentMonth ? 1.0 : 0.8)
                    .offset(y: 12)
            }
        }
        .opacity(isInCurrentMonth ? 1 : 0.5)
        .frame(minHeight: dayItemSize + 8)
        .contentShape(Rectangle())
        .withMinimumTouchTarget()
    }
    
    private var foregroundColor: Color {
        if isSelected {
            return .white
        } else if !isInCurrentMonth {
            return .secondary.opacity(0.5)
        } else if isPast {
            return .secondary
        } else if isToday {
            return .accentColor
        }
        return .primary
    }
    
    private var dayString: String {
        return container.dateTimeService.formatDay(date)
    }
}