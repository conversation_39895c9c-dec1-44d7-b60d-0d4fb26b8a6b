import SwiftUI
import UniformTypeIdentifiers

struct DailyScheduleView: View {
    @EnvironmentObject var container: DIContainer
    @EnvironmentObject var appState: AppState
    @ObservedObject var viewModel: DailyScheduleViewModel
    @State private var showUpdateError = false
    @State private var isTaskBeingDragged = false
    @Environment(\.dynamicTypeSize) private var dynamicTypeSize
    @Environment(\.horizontalSizeClass) private var horizontalSizeClass
    
    private let hours = Array(0..<24)
    
    var body: some View {
        ScrollView {
            ZStack {
                if viewModel.scheduledTasks.isEmpty {
                    emptyStateView
                } else {
                    scheduledContent
                }
            }
        }
        .id("schedule-date-\(Int(viewModel.selectedDate.timeIntervalSince1970 / 86400))")
        .onAppear {
            viewModel.fetchScheduledTasks(for: appState.selectedDate)
        }
        .onChange(of: appState.selectedDate) { newDate in
            viewModel.fetchScheduledTasks(for: newDate)
        }
        .alert("Ошибка планирования", isPresented: $showUpdateError) {
            Button("OK", role: .cancel) { }
        } message: {
            Text("Не удалось запланировать задачу. Возможно, это время уже занято.")
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
    }
    
    private var emptyStateView: some View {
        VStack {
            Spacer()
            EmptyStateView(
                icon: "calendar",
                title: "Нет запланированных задач",
                message: "Перетащите задачи в расписание или нажмите на временной слот, чтобы создать новую задачу"
            )
            .padding()
            Spacer()
        }
        .frame(height: 400) // Using fixed height instead of custom layout
        .padding(.top, 40)
    }
    
    private var scheduledContent: some View {
        LazyVStack(spacing: 0) {
            ForEach(hours, id: \.self) { hour in
                DailyScheduleHourRow(
                    hour: hour,
                    date: viewModel.selectedDate,
                    tasks: viewModel.scheduledTasks(for: hour),
                    onTaskResize: handleTaskResize
                )
                .id("hour-\(hour)")
                .padding(.horizontal)
                .onDrop(of: [UTType.plainText], isTargeted: $isTaskBeingDragged) { providers, _ in
                    handleDrop(providers: providers, hour: hour)
                }
                .contentShape(Rectangle())
                .background(isTaskBeingDragged ? Color.accentColor.opacity(0.05) : Color.clear)
            }
        }
    }
    
    private func handleTaskResize(_ taskId: UUID, _ newDuration: TimeInterval) {
        if let task = viewModel.scheduledTasks.first(where: { $0.id == taskId }) {
            let startTime = container.dateTimeService.getTaskStartTime(task: task)
            
            if !viewModel.updateTaskSchedule(task, startTime: startTime, duration: newDuration) {
                showUpdateError = true
                container.playHaptic(.error)
            } else {
                container.playHaptic(.success)
            }
        }
    }
    
    private func handleDrop(providers: [NSItemProvider], hour: Int) -> Bool {
        guard let provider = providers.first else { return false }
        
        _ = provider.loadObject(ofClass: String.self) { id, error in
            guard 
                error == nil,
                let idString = id,
                let taskId = UUID(uuidString: idString),
                let task = container.dataManager.tasks.first(where: { $0.id == taskId })
            else { return }
            
            let startTime = container.dateTimeService.date(
                bySettingHour: hour,
                minute: 0,
                of: viewModel.selectedDate
            ) ?? viewModel.selectedDate
            
            let duration = task.duration ?? TimeConstants.defaultDuration
            
            DispatchQueue.main.async {
                if !viewModel.updateTaskSchedule(task, startTime: startTime, duration: duration) {
                    showUpdateError = true
                    container.playHaptic(.error)
                } else {
                    container.playHaptic(.success)
                }
            }
        }
        
        return true
    }
}

struct DailyScheduleHourRow: View {
    let hour: Int
    let date: Date
    let tasks: [Task]
    let onTaskResize: (UUID, TimeInterval) -> Void
    
    @EnvironmentObject private var container: DIContainer
    @Environment(\.dynamicTypeSize) private var dynamicTypeSize
    @Environment(\.horizontalSizeClass) private var horizontalSizeClass
    @ScaledMetric private var minRowHeight: CGFloat = 60
    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            HStack {
                Text(formatTime(hour))
                    .font(.caption.monospacedDigit())
                    .foregroundStyle(.secondary)
                    .lineLimit(1)
                    .fixedSize()
                    .frame(width: timeColumnWidth)
                
                Divider()
                    .frame(height: 1)
                    .background(Color.secondary.opacity(0.3))
                    .frame(maxWidth: .infinity)
            }
            .padding(.vertical, 4)
            
            ForEach(tasks) { task in
                TaskBlock(task: task, onResize: onTaskResize)
            }
        }
        .frame(minHeight: minRowHeight)
    }
    
    private var timeColumnWidth: CGFloat {
        switch horizontalSizeClass {
        case .compact:
            return dynamicTypeSize.isAccessibilitySize ? 65 : 50
        default:
            return dynamicTypeSize.isAccessibilitySize ? 80 : 65
        }
    }
    
    private func formatTime(_ hour: Int) -> String {
        let hourDate = container.dateTimeService.date(bySettingHour: hour, minute: 0, of: date) ?? date
        return container.dateTimeService.formatHour(hourDate)
    }
}

struct TaskBlock: View {
    let task: Task
    let onResize: (UUID, TimeInterval) -> Void
    
    @State private var taskHeight: CGFloat = 50
    @State private var initialTaskHeight: CGFloat = 50
    @State private var isResizing = false
    @EnvironmentObject private var container: DIContainer
    @ScaledMetric private var baseTaskHeight: CGFloat = 50
    @ScaledMetric private var minTaskHeight: CGFloat = 30
    @ScaledMetric private var standardPadding: CGFloat = 16
    
    private var taskDuration: TimeInterval {
        task.duration ?? TimeConstants.defaultDuration
    }
    
    private var durationInHours: Double {
        taskDuration / 3600.0
    }
    
    private var taskColor: Color {
        task.category?.color ?? .accentColor
    }
    
    var body: some View {
        VStack(alignment: .leading) {
            Text(task.title)
                .font(.subheadline.weight(.medium))
                .lineLimit(1)
            
            if let description = task.description, !description.isEmpty {
                Text(description)
                    .font(.caption)
                    .foregroundStyle(.secondary)
                    .lineLimit(1)
            }
            
            Spacer()
            
            if let duration = task.duration {
                Text(container.dateTimeService.formatDuration(duration))
                    .font(.caption2)
                    .foregroundStyle(.secondary)
            }
        }
        .padding(.horizontal, standardPadding)
        .padding(.vertical, standardPadding / 2)
        .frame(maxWidth: .infinity, minHeight: taskHeight)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(taskColor.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(taskColor.opacity(0.3), lineWidth: 1)
                )
        )
        .contentShape(Rectangle())
        .onAppear {
            taskHeight = baseTaskHeight * durationInHours
            initialTaskHeight = taskHeight
        }
        .gesture(
            DragGesture()
                .onChanged { value in
                    isResizing = true
                    let newHeight = initialTaskHeight + value.translation.height
                    taskHeight = max(minTaskHeight, newHeight)
                }
                .onEnded { value in
                    isResizing = false
                    initialTaskHeight = taskHeight
                    
                    let roundedDuration = container.dateTimeService.calculateDurationFromHeight(taskHeight, pixelsPerHour: baseTaskHeight)
                    onResize(task.id, roundedDuration)
                }
        )
        .animation(.spring(), value: isResizing)
    }
}