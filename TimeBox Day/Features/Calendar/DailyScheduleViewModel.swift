import Foundation
import Combine
import SwiftUI

class DailyScheduleViewModel: BaseViewModel {
    // MARK: - Published Properties
    @Published private(set) var scheduledTasks: [Task] = []
    @Published var selectedDate: Date
    
    // MARK: - Private Properties
    private let taskService: TaskService
    
    // MARK: - Initialization
    init(container: DIContainer = .shared, selectedDate: Date? = nil) {
        self.selectedDate = selectedDate ?? container.dateTimeService.getDefaultDate()
        self.taskService = container.taskService
        super.init(container: container)
        
        fetchScheduledTasks(for: self.selectedDate)
    }
    
    override func setupBindings() {
        super.setupBindings()
        
        // Подписываемся на изменения задач в TaskService
        taskService.taskChanges
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                guard let self = self else { return }
                self.refreshScheduledTasks()
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Private Methods
    
    /// Обновляет список запланированных задач из TaskService
    private func refreshScheduledTasks() {
        let tasks = taskService.fetchScheduledTasks(forDate: selectedDate)
        
        // Применяем анимацию при обновлении списка
        withAnimation(.spring(
            response: AnimationConstants.springResponse,
            dampingFraction: AnimationConstants.springDamping
        )) {
            self.scheduledTasks = tasks
        }
    }
    
    // MARK: - Public Methods
    
    /// Загружает запланированные задачи для указанной даты
    func fetchScheduledTasks(for date: Date) {
        selectedDate = date
        refreshScheduledTasks()
    }
    
    /// Возвращает запланированные задачи для указанного часа
    func scheduledTasks(for hour: Int) -> [Task] {
        scheduledTasks.filter { task in
            guard let startTime = task.startTime else { return false }
            return container.dateTimeService.getHour(from: startTime) == hour
        }
    }
    
    /// Обновляет расписание задачи, проверяя конфликты с существующими задачами
    func updateTaskSchedule(_ task: Task, startTime: Date, duration: TimeInterval) -> Bool {
        // Проверяем, не пересекается ли новое время с существующими задачами
        if !taskService.validateTimeRange(startTime: startTime, duration: duration, excludingTask: task) {
            return false
        }
        
        return taskService.scheduleTask(task, startTime: startTime, duration: duration)
    }
}