import Foundation
import Combine
import SwiftUI

// MARK: - Константы календаря
enum CalendarConstants {
    static let firstWeekday = 2
    static let daysInWeek = 7
}

final class CalendarViewModel: BaseViewModel {
    // MARK: - Published Properties
    @Published private(set) var weekDates: [Date] = []
    @Published var isMonthViewActive: Bool = false
    
    // MARK: - Private Properties
    private weak var appState: AppState?
    private var displayedMonthDate: Date {
        get {
            isMonthViewActive ? _displayedMonthDate : (appState?.selectedDate ?? container.dateTimeService.getDefaultDate())
        }
        set {
            _displayedMonthDate = newValue
        }
    }
    private var _displayedMonthDate: Date = Date()
    
    // MARK: - Public Methods
    
    /// Получить текущую отображаемую дату
    var currentDisplayedDate: Date {
        displayedMonthDate
    }
    
    // MARK: - Initialization
    init(container: DIContainer, appState: AppState? = nil) {
        self.appState = appState
        super.init(container: container)
        updateWeekDates(for: appState?.selectedDate ?? container.dateTimeService.getDefaultDate())
    }
    
    // MARK: - Override Methods
    override func setupBindings() {
        super.setupBindings()
        
        appState?.$selectedDate
            .sink { [weak self] newDate in
                self?.updateWeekDates(for: newDate)
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Public Methods
    
    /// Переключиться на сегодняшнюю дату
    func goToToday() {
        let today = container.dateTimeService.getTodayDate()
        displayedMonthDate = today
        updateWeekDates(for: today)
        appState?.selectDate(today)
        
        withAnimation(.spring(
            response: AnimationConstants.springResponse,
            dampingFraction: AnimationConstants.springDamping
        )) {
            isMonthViewActive = false
        }
    }
    
    /// Обновить даты недели для указанной даты
    func updateWeekDates(for date: Date) {
        weekDates = container.dateTimeService.getWeekDates(for: date)
    }
    
    /// Перейти к предыдущей неделе
    func moveToPreviousWeek() {
        guard let prevWeekDate = container.dateTimeService.date(byAdjusting: .weekOfYear, value: -1, of: weekDates.first ?? container.dateTimeService.getDefaultDate()) else { return }
        updateWeekDates(for: prevWeekDate)
    }
    
    /// Перейти к следующей неделе
    func moveToNextWeek() {
        guard let nextWeekDate = container.dateTimeService.date(byAdjusting: .weekOfYear, value: 1, of: weekDates.first ?? container.dateTimeService.getDefaultDate()) else { return }
        updateWeekDates(for: nextWeekDate)
    }
    
    /// Переключиться на предыдущий месяц
    func moveToPreviousMonth() {
        if let newDate = container.dateTimeService.date(byAdjusting: .month, value: -1, of: displayedMonthDate) {
            displayedMonthDate = newDate
            updateWeekDates(for: newDate)
        }
    }
    
    /// Переключиться на следующий месяц
    func moveToNextMonth() {
        if let newDate = container.dateTimeService.date(byAdjusting: .month, value: 1, of: displayedMonthDate) {
            displayedMonthDate = newDate
            updateWeekDates(for: newDate)
        }
    }
    
    /// Переключить вид календаря
    func toggleCalendarView() {
        withAnimation(.spring(
            response: AnimationConstants.springResponse,
            dampingFraction: AnimationConstants.springDamping
        )) {
            isMonthViewActive.toggle()
        }
    }
    
    /// Проверить наличие задач на дату
    func hasTasksForDate(_ date: Date) -> Bool {
        container.dataManager.tasks.contains { task in
            let taskDate = task.startTime ?? task.date
            return container.dateTimeService.areDatesInSameDay(taskDate, date)
        }
    }
    
    /// Установить AppState
    func setAppState(_ newAppState: AppState) {
        self.appState = newAppState
        updateWeekDates(for: newAppState.selectedDate)
    }
    
    /// Обновить отображаемый месяц
    func updateDisplayedMonth(_ date: Date) {
        displayedMonthDate = date
    }
    
    // MARK: - Helper Methods
    
    func monthYearString() -> String {
        return container.dateTimeService.formatMonthForCalendar(displayedMonthDate)
    }
    
    func shouldShowTodayButton() -> Bool {
        guard let appState = appState else { return false }
        
        let today = container.dateTimeService.getDefaultDate()
        let currentMonth = container.dateTimeService.calendar.startOfDay(for: today)
        
        let isSelectedDateNotToday = !container.dateTimeService.areDatesInSameDay(appState.selectedDate, today)
        let isDisplayedMonthNotCurrent = isMonthViewActive && 
                                       !container.dateTimeService.areDatesInSameDay(displayedMonthDate, currentMonth)
        let isCurrentWeekNotContainingToday = !isMonthViewActive && 
                                             !weekDates.contains(where: { 
                                                container.dateTimeService.areDatesInSameDay($0, today)
                                             })
        
        return isSelectedDateNotToday || isDisplayedMonthNotCurrent || isCurrentWeekNotContainingToday
    }
}