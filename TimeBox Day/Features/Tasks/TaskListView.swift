import SwiftUI
import UniformTypeIdentifiers

struct TaskListView: View {
    @ObservedObject var viewModel: TaskListViewModel
    @EnvironmentObject private var appState: AppState
    @EnvironmentObject private var container: DIContainer
    
    @State private var isTaskDetailViewPresented = false
    @State private var selectedTask: Task?
    @State private var selectedPriority: TaskPriority?
    
    var body: some View {
        List {
            ForEach(TaskPriority.allCases, id: \.self) { priority in
                Section(header: Text(priority.title)
                    .font(.headline)
                    .foregroundStyle(.primary)
                    .textCase(nil)
                ) {
                    let tasks = viewModel.tasks.filter { $0.priority == priority }
                    
                    if tasks.isEmpty && !viewModel.canAddTask(with: priority) {
                        Text("Нет доступных задач")
                            .foregroundStyle(.secondary)
                    } else {
                        ForEach(tasks) { task in
                            TaskItemView(
                                task: task,
                                onToggle: { viewModel.toggleTaskCompletion(task) },
                                onDelete: { viewModel.deleteTask(task) },
                                onTap: {
                                    selectedTask = task
                                    isTaskDetailViewPresented = true
                                }
                            )
                        }
                        
                        if viewModel.canAddTask(with: priority) {
                            Button {
                                container.playHaptic(.light)
                                selectedPriority = priority
                                selectedTask = nil
                                isTaskDetailViewPresented = true
                            } label: {
                                HStack {
                                    Image(systemName: "plus")
                                        .imageScale(.medium)
                                        .foregroundStyle(.secondary)
                                    
                                    Text("Добавить задачу")
                                        .foregroundStyle(.secondary)
                                }
                            }
                        }
                    }
                }
            }
        }
        .listStyle(.insetGrouped)
        .refreshable {
            viewModel.loadTasks()
        }
        .onAppear {
            viewModel.setAppState(appState)
        }
        .sheet(isPresented: $isTaskDetailViewPresented) {
            if let task = selectedTask {
                TaskDetailView(viewModel: viewModel, task: task)
                    .isPresentedAsSheet(true)
                    .environmentObject(appState)
                    .environmentObject(container)
            } else {
                TaskDetailView(viewModel: viewModel, initialPriority: selectedPriority ?? .tasks)
                    .isPresentedAsSheet(true)
                    .environmentObject(appState)
                    .environmentObject(container)
            }
        }
    }
}

private struct TaskItemView: View {
    let task: Task
    let onToggle: () -> Void
    let onDelete: () -> Void
    let onTap: () -> Void
    @Environment(\.dynamicTypeSize) private var dynamicTypeSize
    @Environment(\.colorScheme) private var colorScheme
    @EnvironmentObject private var container: DIContainer
    
    var body: some View {
        Button(action: onTap) {
            HStack {
                Button {
                    container.playHaptic(.medium)
                    withAnimation(.spring()) {
                        onToggle()
                    }
                } label: {
                    Image(systemName: task.isCompleted ? "checkmark.circle.fill" : "circle")
                        .imageScale(.medium)
                        .symbolRenderingMode(.hierarchical)
                        .foregroundStyle(task.isCompleted ? .green : .secondary)
                        .withMinimumTouchTarget()
                }
                .buttonStyle(ScaleButtonStyle())
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(task.title)
                        .font(dynamicTypeSize > .xxxLarge ? .body : .subheadline)
                        .foregroundStyle(task.isCompleted ? .secondary : .primary)
                        .lineLimit(3)
                    
                    if let description = task.description {
                        Text(description)
                            .font(dynamicTypeSize > .xxxLarge ? .subheadline : .caption)
                            .foregroundStyle(.secondary)
                            .lineLimit(2)
                    }
                    
                    if let startTime = task.startTime {
                        Label(timeFormat(startTime), systemImage: "clock")
                            .font(dynamicTypeSize > .xxxLarge ? .caption : .caption2)
                            .foregroundStyle(.secondary)
                    }
                }
                
                Spacer(minLength: 0)
            }
        }
        .buttonStyle(.plain)
        .swipeActions(edge: .trailing, allowsFullSwipe: true) {
            Button(role: .destructive) {
                container.playHaptic(.success)
                onDelete()
            } label: {
                Label("Delete", systemImage: "trash")
            }
        }
    }
    
    private func timeFormat(_ date: Date) -> String {
        return container.dateTimeService.formatTime(date)
    }
}

private struct ScaleButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}