import SwiftUI
import Combine

// Протокол для тестирования TaskDetailView
protocol TaskViewTestable {
    var title: String { get set }
    var description: String { get set }
    var priority: TaskPriority { get set }
    var isNewTask: Bool { get }
    var task: Task? { get }
    var viewModel: TaskListViewModel { get }
    func addTask() -> Bool
    func saveChanges() -> Bo<PERSON>
}

struct TaskDetailView: View, TaskViewTestable {
    // MARK: - Environment & Dependencies
    @ObservedObject var viewModel: TaskListViewModel
    @EnvironmentObject private var appState: AppState
    @EnvironmentObject private var container: DIContainer
    @Environment(\.dismiss) var dismiss
    @Environment(\.presentationMode) var presentationMode
    @Environment(\.presentationIsPresentedAsSheet) private var isPresentedAsSheet
    
    // MARK: - State
    @State var title: String
    @State var description: String
    @State var priority: TaskPriority
    @State var isScheduled: Bool
    @State var startTime: Date
    @State var duration: TimeInterval
    @State var selectedCategory: TaskCategory?
    @State var isProcessing = false
    @State private var isEditing = false
    @State var showSchedule = false
    @FocusState var focusedField: Field?
    
    // MARK: - Properties
    let task: Task?
    let isNewTask: Bool
    
    // Получаем категории из контейнера
    private var categories: [TaskCategory] {
        container.categoryService.categories
    }

    enum Field {
        case title, description
    }
    
    // Стандартная продолжительность задачи (30 минут)
    private let defaultDuration: TimeInterval = 1800
    
    // MARK: - Dynamic Metrics
    @ScaledMetric private var taskStatusIconSize: CGFloat = 20
    @ScaledMetric private var indicatorSize: CGFloat = 6
    @ScaledMetric private var chevronSize: CGFloat = 12
    
    // MARK: - Initialization
    init(viewModel: TaskListViewModel, task: Task? = nil, initialPriority: TaskPriority = .tasks) {
        self.viewModel = viewModel
        self.task = task
        self.isNewTask = task == nil
        self.isEditing = task == nil // Новая задача сразу в режиме редактирования
        
        // Initialize state properties
        _title = State(initialValue: task?.title ?? "")
        _description = State(initialValue: task?.description ?? "")
        _priority = State(initialValue: task?.priority ?? initialPriority)
        _selectedCategory = State(initialValue: task?.category)
        
        // Инициализация полей планирования
        let hasSchedule = task?.startTime != nil && task?.duration != nil
        _isScheduled = State(initialValue: hasSchedule)
        _startTime = State(initialValue: DIContainer.shared.dateTimeService.getTaskStartTime(task: task))
        _duration = State(initialValue: task?.duration ?? DIContainer.shared.dateTimeService.getDefaultDuration())
    }
    
    // MARK: - Body
    var body: some View {
        Group {
            if isPresentedAsSheet {
                NavigationStack {
                    taskContent
                        .navigationTitle(isNewTask ? "Новая задача" : task?.title ?? "")
                        .navigationBarTitleDisplayMode(isNewTask ? .inline : .large)
                        .toolbar {
                            toolbarContent
                        }
                }
                .presentationDetents([.medium, .large])
                .interactiveDismissDisabled(isEditing && isNewTask)
            } else {
                taskContent
                    .navigationBarBackButtonHidden(isEditing && isNewTask)
                    .navigationTitle(isNewTask ? "Новая задача" : task?.title ?? "")
                    .navigationBarTitleDisplayMode(isNewTask ? .inline : .large)
                    .toolbar {
                        toolbarContent
                    }
            }
        }
        .onAppear {
            if isNewTask {
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    focusedField = .title
                }
            }
        }
    }
    
    // MARK: - Views
    
    @ViewBuilder
    private var taskContent: some View {
        if (!isEditing && !isNewTask) {
            taskDetailsView
        } else {
            taskEditView
                .scrollDismissesKeyboard(.interactively)
        }
    }
    
    @ToolbarContentBuilder
    private var toolbarContent: some ToolbarContent {
        if !isEditing && !isNewTask {
            ToolbarItem(placement: .primaryAction) {
                Button("Редактировать") {
                    isEditing = true
                }
            }
            
            ToolbarItem(placement: .cancellationAction) {
                Button("Закрыть") {
                    dismiss()
                }
            }
        } else {
            ToolbarItem(placement: .cancellationAction) {
                Button(isNewTask ? "Отмена" : "Закрыть") {
                    if isNewTask {
                        dismiss()
                    } else {
                        isEditing = false
                    }
                }
            }
            
            ToolbarItem(placement: .confirmationAction) {
                Button(isNewTask ? "Добавить" : "Сохранить") {
                    let success = isNewTask ? addTask() : saveChanges()
                    if success {
                        if isNewTask {
                            dismiss()
                        } else {
                            isEditing = false
                        }
                    } else {
                        container.playHaptic(.error)
                    }
                }
                .disabled(title.isEmpty || isProcessing)
                .opacity(title.isEmpty ? 0.6 : 1.0)
            }
        }
    }
    
    private var taskDetailsView: some View {
        List {
            // Task details section
            Section {
                Text(title)
                    .font(.headline)
                
                if !description.isEmpty {
                    Text(description)
                        .font(.body)
                        .foregroundStyle(.secondary)
                }
            }
            
            // Priority section
            Section(header: Text("Priority")) {
                Label {
                    Text(priority.title)
                } icon: {
                    priorityIcon(for: priority)
                }
            }
            
            // Category section
            if let category = selectedCategory {
                Section(header: Text("Категория")) {
                    CategoryBadgeView(title: category.name, color: category.color)
                }
            }
            
            // Schedule section
            if isScheduled {
                Section(header: Text("Расписание")) {
                    LabeledContent("Время начала") {
                        Text(formatTime(startTime))
                    }
                    
                    LabeledContent("Продолжительность") {
                        Text(formatDuration(duration))
                    }
                    
                    if let endTime = calculateEndTime() {
                        LabeledContent("Окончание") {
                            Text(formatTime(endTime))
                        }
                    }
                }
            }
            
            // Status section
            if let task = task {
                Section(header: Text("Status")) {
                    Button {
                        viewModel.toggleTaskCompletion(task)
                        dismiss()
                    } label: {
                        HStack {
                            Image(systemName: task.isCompleted ? "checkmark.circle.fill" : "circle")
                                .imageScale(.medium)
                                .frame(width: taskStatusIconSize, height: taskStatusIconSize)
                                .symbolRenderingMode(.hierarchical)
                                .foregroundStyle(task.isCompleted ? PriorityColors.completed : .secondary)
                            
                            Text(task.isCompleted ? "Отметить как невыполненную" : "Отметить как выполненную")
                        }
                    }
                }
            }
        }
    }
    
    private var taskEditView: some View {
        List {
            // Task details section
            Section {
                TextField("Название задачи", text: $title)
                    .focused($focusedField, equals: .title)
                    .font(.headline)
                    .submitLabel(.next)
                    .onSubmit {
                        focusedField = .description
                    }
                
                TextEditor(text: $description)
                    .focused($focusedField, equals: .description)
                    .lineLimit(3...6) // Используем конкретные значения вместо TaskLayout констант
                    .font(.body)
            }
            
            // Priority section
            Section(header: Text("Приоритет")) {
                if isNewTask {
                    Picker("Приоритет", selection: $priority) {
                        ForEach(TaskPriority.allCases, id: \.self) { priorityOption in
                            if canSetPriority(to: priorityOption) {
                                Label {
                                    Text(priorityOption.title)
                                } icon: {
                                    priorityIcon(for: priorityOption)
                                }.tag(priorityOption)
                            }
                        }
                    }
                    .pickerStyle(.navigationLink)
                    .withMinimumTouchTarget()
                } else {
                    Picker("Приоритет", selection: $priority) {
                        ForEach(TaskPriority.allCases, id: \.self) { priorityOption in
                            if canSetPriority(to: priorityOption) {
                                Label {
                                    Text(priorityOption.title)
                                } icon: {
                                    priorityIcon(for: priorityOption)
                                }.tag(priorityOption)
                            }
                        }
                    }
                    .pickerStyle(.inline)
                }
            }
            
            // Category section
            Section(header: Text("Категория")) {
                Picker("Категория", selection: $selectedCategory) {
                    Text("Нет").tag(nil as TaskCategory?)
                    ForEach(categories) { category in
                        CategoryBadgeView(title: category.name, color: category.color)
                            .tag(Optional(category))
                    }
                }
                .pickerStyle(.navigationLink)
                .withMinimumTouchTarget()
            }
            
            // Schedule section
            Section(header: Text("Расписание")) {
                Toggle("Запланировать по времени", isOn: $isScheduled)
                
                if isScheduled {
                    List {
                        Label {
                            HStack {
                                Text(formatTime(startTime))
                                Spacer()
                                Image(systemName: "chevron.right")
                                    .foregroundStyle(.secondary)
                                    .imageScale(.medium)
                                    .frame(width: chevronSize, height: chevronSize)
                            }
                        } icon: {
                            Image(systemName: "calendar")
                                .foregroundStyle(.secondary)
                        }
                        .withMinimumTouchTarget()
                        .onTapGesture {
                            container.playHaptic(.light)
                            showSchedule = true
                        }
                        
                        Label {
                            Text(duration.description)
                        } icon: {
                            Image(systemName: "clock.fill")
                                .foregroundStyle(.secondary)
                        }
                        .withMinimumTouchTarget()
                        
                        if let endTime = calculateEndTime() {
                            Label {
                                Text(formatTime(endTime))
                            } icon: {
                                Image(systemName: "clock")
                                    .foregroundStyle(.secondary)
                            }
                            .withMinimumTouchTarget()
                        }
                    }
                    .listStyle(.plain)
                }
            }
            
            // Status section (only for existing tasks)
            if !isNewTask, let task = task {
                Section(header: Text("Статус")) {
                    Button {
                        viewModel.toggleTaskCompletion(task)
                        dismiss()
                    } label: {
                        HStack {
                            Image(systemName: task.isCompleted ? "checkmark.circle.fill" : "circle")
                                .imageScale(.medium)
                                .symbolRenderingMode(.hierarchical)
                                .foregroundStyle(task.isCompleted ? PriorityColors.completed : .secondary)
                            
                            Text(task.isCompleted ? "Отметить как невыполненную" : "Отметить как выполненную")
                        }
                    }
                }
                
                // Delete section
                Section {
                    Button(role: .destructive) {
                        container.playHaptic(.success)
                        withAnimation(.spring(
                            response: AnimationConstants.springResponse,
                            dampingFraction: AnimationConstants.springDamping
                        )) {
                            presentationMode.wrappedValue.dismiss()
                            DispatchQueue.main.asyncAfter(deadline: .now() + AnimationConstants.duration) {
                                viewModel.deleteTask(task)
                            }
                        }
                    } label: {
                        Label("Удалить задачу", systemImage: "trash")
                            .frame(maxWidth: .infinity)
                    }
                    .withMinimumTouchTarget()
                }
            }
        }
    }
    
    // MARK: - Helper Methods
    func priorityIcon(for priority: TaskPriority) -> some View {
        switch priority {
        case .theOne:
            return Image(systemName: "exclamationmark.circle.fill")
                .foregroundColor(PriorityColors.theOne)
        case .tasks:
            return Image(systemName: "circle.fill")
                .foregroundColor(PriorityColors.tasks)
        }
    }
    
    func canSetPriority(to priority: TaskPriority) -> Bool {
        if let task = task, priority == task.priority {
            return true
        }
        
        // For new tasks or changing priority of existing tasks
        let existingTasks = viewModel.tasks
        let tasksWithPriority = existingTasks.filter { $0.priority == priority && (task == nil || $0.id != task!.id) }
        return tasksWithPriority.count < priority.maxTasks
    }
    
    func formatTime(_ date: Date) -> String {
        return container.dateTimeService.formatTime(date)
    }
    
    func formatDuration(_ duration: TimeInterval) -> String {
        return container.dateTimeService.formatDuration(duration)
    }
    
    func calculateEndTime() -> Date? {
        return container.dateTimeService.calculateEndTime(startTime: startTime, duration: duration)
    }
    
    @discardableResult
    func addTask() -> Bool {
        guard !title.isEmpty else { return false }
        
        isProcessing = true
        let newTask = Task(
            title: title,
            description: description.isEmpty ? nil : description,
            priority: priority,
            date: appState.selectedDate,
            category: selectedCategory,
            startTime: isScheduled ? startTime : nil,
            duration: isScheduled ? duration : nil
        )
        
        let success = viewModel.addTask(newTask)
        if success {
            container.playHaptic(.success)
            DispatchQueue.main.async {
                dismiss()
            }
        } else {
            container.playHaptic(.error)
        }
        isProcessing = false
        return success
    }
    
    @discardableResult
    func saveChanges() -> Bool {
        guard !title.isEmpty, let originalTask = task else { return false }
        
        isProcessing = true
        
        // Проверка, изменилось ли расписание и нет ли конфликта
        let hasScheduleChanged = (isScheduled != originalTask.isScheduled) || 
                               (isScheduled && (startTime != originalTask.startTime || duration != originalTask.duration))
        
        if hasScheduleChanged && isScheduled {
            // Проверяем через TaskService, что нет пересечений с другими задачами
            if !container.taskService.validateTimeRange(startTime: startTime, duration: duration, excludingTask: originalTask) {
                container.playHaptic(.error)
                isProcessing = false
                return false
            }
        }
        
        // Обновляем задачу через ViewModel
        let success = viewModel.updateTask(
            originalTask, 
            title: title,
            description: description.isEmpty ? nil : description,
            priority: priority,
            isCompleted: originalTask.isCompleted,
            date: originalTask.date,
            category: selectedCategory,
            startTime: isScheduled ? startTime : nil,
            duration: isScheduled ? duration : nil
        )
        
        if success {
            container.playHaptic(.success)
            DispatchQueue.main.async {
                dismiss()
            }
        } else {
            container.playHaptic(.error)
        }
        isProcessing = false
        return success
    }
    
    private var categoryView: some View {
        if let category = task?.category {
            CategoryBadgeView(title: category.name, color: category.color)
                .opacity(1) // Adding consistent modifier to match the other branch
        } else {
            CategoryBadgeView(title: "", color: .clear)
                .opacity(0)
        }
    }
    
    private var categoryButton: some View {
        if let category = selectedCategory {
            CategoryBadgeView(title: category.name, color: category.color)
                .opacity(1) // Adding consistent modifier to match the other branch
        } else {
            CategoryBadgeView(title: "", color: .clear)
                .opacity(0)
        }
    }
}

// MARK: - Environment Property

private struct PresentationIsSheetKey: EnvironmentKey {
    static let defaultValue: Bool = false
}

extension EnvironmentValues {
    var presentationIsPresentedAsSheet: Bool {
        get { self[PresentationIsSheetKey.self] }
        set { self[PresentationIsSheetKey.self] = newValue }
    }
}

extension View {
    func isPresentedAsSheet(_ isSheet: Bool) -> some View {
        environment(\.presentationIsPresentedAsSheet, isSheet)
    }
}

// MARK: - Previews
struct TaskDetailView_Previews: PreviewProvider {
    static var previews: some View {
        let container = DIContainer.shared
        let appState = AppState(container: container)
        let viewModel = TaskListViewModel(container: container)
        
        Group {
            // Новая задача
            TaskDetailView(viewModel: viewModel)
                .isPresentedAsSheet(true)
            
            // Существующая задача
            TaskDetailView(viewModel: viewModel, task: Task(title: "Тестовая задача", description: "Описание задачи", priority: .tasks))
                .isPresentedAsSheet(false)
        }
        .environmentObject(appState)
        .environmentObject(container)
    }
}