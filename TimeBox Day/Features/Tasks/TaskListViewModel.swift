@preconcurrency import Foundation
import SwiftUI
import Combine

protocol TaskPriorityServiceProtocol {
    func canAddTask(with priority: TaskPriority, existingTasks: [Task], forDate: Date) -> Bool
    func validateTaskPriority(_ task: Task, existingTasks: [Task]) -> <PERSON><PERSON>
}

class TaskPriorityService: TaskPriorityServiceProtocol {
    private let dateTimeService: DateTimeServiceProtocol
    
    init(dateTimeService: DateTimeServiceProtocol = StandardDateTimeService.shared) {
        self.dateTimeService = dateTimeService
    }
    
    func canAddTask(with priority: TaskPriority, existingTasks: [Task], forDate: Date) -> Bool {
        let tasksWithPriority = existingTasks.filter { task in
            task.priority == priority && 
            dateTimeService.areDatesInSameDay(task.date, forDate)
        }
        
        let taskCount = tasksWithPriority.count
        return taskCount < priority.maxTasks
    }
    
    func validateTaskPriority(_ task: Task, existingTasks: [Task]) -> <PERSON><PERSON> {
        return canAddTask(with: task.priority, existingTasks: existingTasks.filter { $0.id != task.id }, forDate: task.date)
    }
}

final class TaskListViewModel: BaseViewModel {
    @Published var tasks: [Task] = []
    @Published var selectedFilter: TaskPriority?
    
    private let taskService: TaskService
    private var appState: AppState?
    
    override init(container: DIContainer) {
        self.taskService = container.taskService
        super.init(container: container)
        setupBindings()
    }
    
    override func setupBindings() {
        // Подписываемся на изменения задач в TaskService и изменения выбранной даты
        Publishers.Merge(
            taskService.taskChanges,
            appState?.$selectedDate.map { _ in }.eraseToAnyPublisher() ?? Empty().eraseToAnyPublisher()
        )
        .receive(on: DispatchQueue.main)
        .sink { [weak self] _ in
            self?.loadTasks()
        }
        .store(in: &cancellables)
    }
    
    func loadTasks() {
        if let selectedDate = appState?.selectedDate {
            tasks = taskService.fetchTasks(forDate: selectedDate)
        } else if let selectedFilter = selectedFilter {
            tasks = taskService.fetchTasksByPriority(selectedFilter)
        } else {
            tasks = taskService.fetchAllTasks()
        }
    }
    
    func fetchTasks(for date: Date) {
        // Этот метод теперь просто обновляет AppState, который триггерит loadTasks через binding
        appState?.selectDate(date)
    }
    
    // MARK: - Task Management Methods
    
    /// Добавляет новую задачу
    @discardableResult
    func addTask(_ task: Task) -> Bool {
        // Проверим, можно ли добавить задачу с таким приоритетом
        guard canAddTask(with: task.priority) else {
            return false
        }
        
        return taskService.saveTask(task)
    }
    
    /// Обновляет существующую задачу
    @discardableResult
    func updateTask(_ task: Task, title: String? = nil, description: String? = nil, priority: TaskPriority? = nil) -> Bool {
        return taskService.updateTask(task, title: title, description: description, priority: priority)
    }
    
    /// Обновляет задачу с полным набором параметров
    @discardableResult
    func updateTask(_ task: Task, 
                    title: String? = nil,
                    description: String? = nil,
                    priority: TaskPriority? = nil,
                    isCompleted: Bool? = nil,
                    date: Date? = nil,
                    category: TaskCategory? = nil,
                    startTime: Date? = nil,
                    duration: TimeInterval? = nil) -> Bool {
        
        var updatedTask = task
        
        // Обновляем базовые поля
        if let title = title { updatedTask.title = title }
        if let description = description { updatedTask.description = description }
        if let priority = priority { updatedTask.priority = priority }
        if let isCompleted = isCompleted { updatedTask.isCompleted = isCompleted }
        if let date = date { updatedTask.date = date }
        updatedTask.category = category // nil тоже валидное значение
        
        // Обновляем расписание
        updatedTask.startTime = startTime
        updatedTask.duration = duration
        
        return taskService.saveTask(updatedTask)
    }
    
    /// Переключает состояние выполнения задачи
    func toggleTaskCompletion(_ task: Task) {
        taskService.toggleTaskCompletion(task)
    }
    
    /// Удаляет задачу
    func deleteTask(_ task: Task) {
        taskService.deleteTask(task)
    }
    
    // MARK: - Filter Methods
    
    /// Фильтрует задачи по приоритету
    func filterTasks(by priority: TaskPriority?) {
        selectedFilter = priority
        loadTasks()
    }
    
    /// Возвращает задачи с указанным приоритетом
    func tasksForPriority(_ priority: TaskPriority) -> [Task] {
        return tasks.filter { $0.priority == priority }
    }
    
    // MARK: - Validation Methods
    
    /// Проверяет, можно ли добавить задачу с указанным приоритетом
    func canAddTask(with priority: TaskPriority) -> Bool {
        let dateForCheck = appState?.selectedDate ?? container.dateTimeService.getDefaultDate()
        return taskService.canAddTask(with: priority, forDate: dateForCheck)
    }
    
    // MARK: - AppState Integration
    func setAppState(_ appState: AppState) {
        self.appState = appState
        // Настраиваем привязку к изменениям даты
        setupBindings()
        // Загружаем начальные данные
        loadTasks()
    }
}