import SwiftUI

/// Базовый протокол для всех координаторов в приложении
protocol Coordinator: ObservableObject {
    /// Навигационный путь координатора
    var path: NavigationPath { get set }
    
    /// Переход к определенному пункту назначения
    func navigate(to destination: AppDestination)
    
    /// Сбросить навигационный стек до корня
    func popToRoot()
    
    /// Удалить верхнее представление из навигационного стека
    func pop()
}

/// Реализация по умолчанию для протокола Coordinator
extension Coordinator {
    func navigate(to destination: AppDestination) {
        path.append(destination)
    }
    
    func popToRoot() {
        path = NavigationPath()
    }
    
    func pop() {
        if (!path.isEmpty) {
            path.removeLast()
        }
    }
}

/// Перечисление, представляющее различные пункты назначения в приложении
enum AppDestination: Hashable {
    // Task destinations
    case taskDetail(Task)
    case addTask
    case editTask(Task)
    
    // Category destinations
    case categoryList
    case categoryDetail(TaskCategory)
    case addCategory
    case editCategory(TaskCategory)
    
    // Settings
    case settings
    
    func hash(into hasher: inout Hasher) {
        switch self {
        case .taskDetail(let task):
            hasher.combine(0)
            hasher.combine(task.id)
        case .addTask:
            hasher.combine(1)
        case .editTask(let task):
            hasher.combine(2)
            hasher.combine(task.id)
        case .categoryList:
            hasher.combine(3)
        case .categoryDetail(let category):
            hasher.combine(4)
            hasher.combine(category.id)
        case .addCategory:
            hasher.combine(5)
        case .editCategory(let category):
            hasher.combine(6)
            hasher.combine(category.id)
        case .settings:
            hasher.combine(7)
        }
    }
    
    static func == (lhs: AppDestination, rhs: AppDestination) -> Bool {
        switch (lhs, rhs) {
        case (.taskDetail(let lhsTask), .taskDetail(let rhsTask)):
            return lhsTask.id == rhsTask.id
        case (.addTask, .addTask):
            return true
        case (.editTask(let lhsTask), .editTask(let rhsTask)):
            return lhsTask.id == rhsTask.id
        case (.categoryList, .categoryList):
            return true
        case (.categoryDetail(let lhsCategory), .categoryDetail(let rhsCategory)):
            return lhsCategory.id == rhsCategory.id
        case (.addCategory, .addCategory):
            return true
        case (.editCategory(let lhsCategory), .editCategory(let rhsCategory)):
            return lhsCategory.id == rhsCategory.id
        case (.settings, .settings):
            return true
        default:
            return false
        }
    }
}