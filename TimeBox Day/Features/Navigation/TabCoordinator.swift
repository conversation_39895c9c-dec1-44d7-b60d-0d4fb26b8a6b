import SwiftUI
import Combine

/// Координатор для управления навигацией на основе вкладок в приложении
class TabCoordinator: ObservableObject {
    // MARK: - Published Properties
    
    /// Текущая выбранная вкладка
    @Published var selectedTab: TabItem = .schedule
    
    /// Единый навигационный путь для всего приложения
    @Published var navigationPath = NavigationPath()
    
    /// Выбранный приоритет при создании новой задачи
    @Published var selectedPriority: TaskPriority?
    
    // MARK: - Tab Items
    
    /// Перечисление, представляющее основные вкладки в приложении
    enum TabItem: Hashable {
        case tasks
        case schedule
        case categories
        case settings
        
        var title: String {
            switch self {
            case .tasks: return "Tasks"
            case .schedule: return "Days"
            case .categories: return "Categories"
            case .settings: return "Settings"
            }
        }
        
        var iconName: String {
            switch self {
            case .tasks: return "checkmark.circle"
            case .schedule: return "calendar"
            case .categories: return "tag"
            case .settings: return "gear"
            }
        }
    }
    
    // MARK: - Navigation Methods
    
    /// Получить навигационный путь для текущей вкладки
    var currentPath: Binding<NavigationPath> {
        Binding(get: { self.navigationPath }, set: { self.navigationPath = $0 })
    }
    
    /// Перейти к пункту назначения в текущей вкладке
    func navigate(to destination: AppDestination) {
        navigationPath.append(destination)
    }
    
    /// Удалить верхнее представление из навигационного стека текущей вкладки
    func pop() {
        if !navigationPath.isEmpty {
            navigationPath.removeLast()
        }
    }
    
    /// Сбросить навигационный стек для текущей вкладки
    func popToRoot() {
        navigationPath = NavigationPath()
    }
    
    /// Переключиться на конкретную вкладку
    func switchTab(to tab: TabItem) {
        // Сохраняем предыдущую вкладку для возможной обработки действий при переключении
        let previousTab = selectedTab
        
        // Устанавливаем новую выбранную вкладку
        selectedTab = tab
        
        // Сбрасываем навигационный путь при переключении вкладок для чистого опыта навигации
        // Это можно настроить в зависимости от требований UX
        if previousTab != tab {
            popToRoot()
        }
    }
}