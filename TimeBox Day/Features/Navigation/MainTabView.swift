import SwiftUI

struct MainTabView: View {
    @StateObject private var coordinator = TabCoordinator()
    @EnvironmentObject var appState: AppState
    @EnvironmentObject private var container: DIContainer
    
    var body: some View {
        NavigationStack(path: coordinator.currentPath) {
            TabView(selection: $coordinator.selectedTab) {
                TasksRootView()
                    .tabItem {
                        Label(TabCoordinator.TabItem.tasks.title, 
                              systemImage: TabCoordinator.TabItem.tasks.iconName)
                    }
                    .tag(TabCoordinator.TabItem.tasks)
                
                ScheduleRootView()
                    .tabItem {
                        Label(TabCoordinator.TabItem.schedule.title, 
                              systemImage: TabCoordinator.TabItem.schedule.iconName)
                    }
                    .tag(TabCoordinator.TabItem.schedule)
                
                CategoriesRootView()
                    .tabItem {
                        Label(TabCoordinator.TabItem.categories.title, 
                              systemImage: TabCoordinator.TabItem.categories.iconName)
                    }
                    .tag(TabCoordinator.TabItem.categories)
                
                SettingsView()
                    .tabItem {
                        Label(TabCoordinator.TabItem.settings.title, 
                              systemImage: TabCoordinator.TabItem.settings.iconName)
                    }
                    .tag(TabCoordinator.TabItem.settings)
            }
            .navigationDestination(for: AppDestination.self) { destination in
                destinationView(for: destination)
            }
        }
        .environmentObject(coordinator)
    }
    
    @ViewBuilder
    private func destinationView(for destination: AppDestination) -> some View {
        switch destination {
        case .taskDetail(let task):
            TaskDetailView(viewModel: container.taskListViewModel, task: task)
                .isPresentedAsSheet(false)
        case .addTask:
            TaskDetailView(
                viewModel: container.taskListViewModel,
                initialPriority: coordinator.selectedPriority ?? .tasks
            ).isPresentedAsSheet(true)
        case .editTask(let task):
            TaskDetailView(viewModel: container.taskListViewModel, task: task)
                .isPresentedAsSheet(false)
        case .categoryList:
            CategoriesView()
        case .categoryDetail(let category):
            TaskListView(viewModel: TaskListViewModel(container: container))
                .navigationTitle(category.name)
        case .addCategory:
            AddCategoryView { 
                container.dataManager.fetchAllCategories()
            }
        case .editCategory(let category):
            AddCategoryView(editingCategory: category) {
                container.dataManager.fetchAllCategories()
            }
        case .settings:
            SettingsView()
        }
    }
}

struct TasksRootView: View {
    @EnvironmentObject var coordinator: TabCoordinator
    @EnvironmentObject var appState: AppState
    @EnvironmentObject var container: DIContainer
    
    var body: some View {
        TaskListView(viewModel: container.taskListViewModel)
            .onAppear {
                container.taskListViewModel.fetchTasks(for: appState.selectedDate)
            }
            .onChange(of: appState.selectedDate) { newDate in
                container.taskListViewModel.fetchTasks(for: newDate)
            }
            .navigationTitle("Задачи")
    }
}

struct ScheduleContainer: View {
    @EnvironmentObject var appState: AppState
    @EnvironmentObject var container: DIContainer
    @StateObject private var scheduleViewModel: DailyScheduleViewModel
    @StateObject private var taskListViewModel: TaskListViewModel
    
    init() {
        _scheduleViewModel = StateObject(wrappedValue: DailyScheduleViewModel(container: DIContainer.shared))
        _taskListViewModel = StateObject(wrappedValue: TaskListViewModel(container: DIContainer.shared))
    }
    
    var body: some View {
        VStack(spacing: 0) {
            CalendarView()
                .background(.background)
                .shadow(radius: 2, y: 1)
            
            HStack {
                TaskListView(viewModel: taskListViewModel)
                    .layoutPriority(1)
                
                Divider()
                
                DailyScheduleView(viewModel: scheduleViewModel)
                    .layoutPriority(1)
            }
        }
        .onAppear {
            scheduleViewModel.fetchScheduledTasks(for: appState.selectedDate)
            taskListViewModel.fetchTasks(for: appState.selectedDate)
        }
        .onChange(of: appState.selectedDate) { newDate in
            scheduleViewModel.fetchScheduledTasks(for: newDate)
            taskListViewModel.fetchTasks(for: newDate)
        }
    }
}

struct ScheduleRootView: View {
    var body: some View {
        ScheduleContainer()
    }
}

struct CategoriesRootView: View {
    @EnvironmentObject var coordinator: TabCoordinator
    @EnvironmentObject var container: DIContainer
    
    var body: some View {
        CategoriesView()
            .navigationTitle("Категории")
    }
}
