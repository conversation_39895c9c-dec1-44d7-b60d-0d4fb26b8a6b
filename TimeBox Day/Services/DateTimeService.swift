import Foundation

// MARK: - DateTimeServiceProtocol
protocol DateTimeServiceProtocol {
    var calendar: Calendar { get }
    var timeZone: TimeZone { get }
    var currentDate: Date { get }
    
    // MARK: - Configuration
    func setTimeZone(_ timeZone: TimeZone)
    
    // MARK: - Date Comparison
    func isDateInPast(_ date: Date) -> Bool
    func isDateToday(_ date: Date) -> Bool
    func areDatesInSameDay(_ date1: Date, _ date2: Date) -> Bool
    
    // MARK: - Date Manipulation
    func date(bySettingHour hour: Int, minute: Int, of date: Date) -> Date?
    func date(byAdjusting component: Calendar.Component, value: Int, of date: Date?) -> Date?
    func startOfDay(for date: Date) -> Date
    func endOfDay(for date: Date) -> Date
    
    // MARK: - Calendar Navigation
    func getTodayDate() -> Date
    func getWeekDates(for date: Date) -> [Date]
    func getMonthWeeks(for date: Date) -> [[Date]]
    
    // MARK: - Time Operations
    func roundedTime(from date: Date) -> Date
    func getHour(from date: Date) -> Int
    func getMinute(from date: Date) -> Int
    func validateTimeRange(startTime: Date, duration: TimeInterval, existingTasks: [Task]) -> Bool
    func calculateDurationFromHeight(_ height: CGFloat, pixelsPerHour: CGFloat) -> TimeInterval
    func calculateEndTime(startTime: Date, duration: TimeInterval) -> Date
    
    // MARK: - Default Values
    func getDefaultDate() -> Date
    func getDefaultStartTime() -> Date
    func getDefaultDuration() -> TimeInterval
    
    // MARK: - Task Date Helpers
    func getTaskDate(task: Task?, defaultDate: Date?) -> Date
    func getTaskStartTime(task: Task?) -> Date
    
    // MARK: - Formatting
    func formatTime(_ date: Date) -> String
    func formatDay(_ date: Date) -> String
    func formatMonthForCalendar(_ date: Date) -> String
    func formatHour(_ date: Date) -> String
    func formatDuration(_ duration: TimeInterval) -> String
    func getShortWeekdaySymbols() -> [String]
    
    // MARK: - Predicate Helpers
    func createDateRangePredicate(for date: Date, keyPath: String) -> NSPredicate
    func createTimeRangePredicate(for date: Date, keyPath: String) -> NSPredicate
}

// MARK: - DateFormatStyle
private enum DateFormatStyle {
    case time
    case day
    case monthYear
    case hour
    
    var dateFormat: String {
        switch self {
        case .time: return "HH:mm"
        case .day: return "d"
        case .monthYear: return "MMMM yyyy"
        case .hour: return "HH:00"
        }
    }
}

/// Стандартная реализация сервиса для работы с датами и временем
final class StandardDateTimeService: DateTimeServiceProtocol {
    // MARK: - Shared Instance
    static let shared = StandardDateTimeService()
    
    // MARK: - Calendar Properties
    private(set) var calendar: Calendar
    private(set) var timeZone: TimeZone
    private var formatters: [DateFormatStyle: DateFormatter] = [:]
    
    var currentDate: Date {
        getTodayDate()
    }
    
    // MARK: - Initialization
    private init() {
        self.timeZone = TimeZone.current
        var calendar = Calendar.current
        calendar.firstWeekday = 2 // Понедельник как первый день недели
        calendar.timeZone = timeZone
        self.calendar = calendar
    }
    
    // MARK: - Configuration
    func setTimeZone(_ timeZone: TimeZone) {
        self.timeZone = timeZone
        var calendar = self.calendar
        calendar.timeZone = timeZone
        self.calendar = calendar
        formatters.removeAll()
    }
    
    // MARK: - Date Comparison
    func isDateInPast(_ date: Date) -> Bool {
        return calendar.compare(date, to: Date(), toGranularity: .day) == .orderedAscending
    }
    
    func isDateToday(_ date: Date) -> Bool {
        return calendar.isDateInToday(date)
    }
    
    func areDatesInSameDay(_ date1: Date, _ date2: Date) -> Bool {
        return calendar.isDate(date1, inSameDayAs: date2)
    }
    
    // MARK: - Date Manipulation
    func date(bySettingHour hour: Int, minute: Int, of date: Date) -> Date? {
        return calendar.date(bySettingHour: hour, minute: minute, second: 0, of: date)
    }
    
    func date(byAdjusting component: Calendar.Component, value: Int, of date: Date?) -> Date? {
        guard let date = date else { return nil }
        return calendar.date(byAdding: component, value: value, to: date)
    }
    
    func startOfDay(for date: Date) -> Date {
        return calendar.startOfDay(for: date)
    }
    
    func endOfDay(for date: Date) -> Date {
        guard let nextDay = calendar.date(byAdding: .day, value: 1, to: startOfDay(for: date)) else {
            return date
        }
        return calendar.date(byAdding: .second, value: -1, to: nextDay) ?? date
    }
    
    // MARK: - Calendar Navigation
    func getTodayDate() -> Date {
        return Date()
    }
    
    func getWeekDates(for date: Date) -> [Date] {
        let startOfWeek = calendar.date(from: 
            calendar.dateComponents([.yearForWeekOfYear, .weekOfYear], from: date)
        ) ?? date
        
        return (0..<7).compactMap { dayOffset in
            calendar.date(byAdding: .day, value: dayOffset, to: startOfWeek)
        }
    }
    
    func getMonthWeeks(for date: Date) -> [[Date]] {
        guard let monthInterval = calendar.dateInterval(of: .month, for: date) else {
            return []
        }
        
        let firstWeekStart = calendar.date(from: 
            calendar.dateComponents([.yearForWeekOfYear, .weekOfYear], from: monthInterval.start)
        ) ?? monthInterval.start
        
        let lastDay = calendar.date(byAdding: DateComponents(day: -1), to: monthInterval.end) ?? monthInterval.end
        let lastWeekStart = calendar.date(from:
            calendar.dateComponents([.yearForWeekOfYear, .weekOfYear], from: lastDay)
        ) ?? lastDay
        
        var weeks: [[Date]] = []
        var currentDate = firstWeekStart
        
        while currentDate <= lastWeekStart {
            weeks.append(getWeekDates(for: currentDate))
            currentDate = calendar.date(byAdding: .weekOfYear, value: 1, to: currentDate) ?? lastWeekStart
        }
        
        return weeks
    }
    
    // MARK: - Time Operations
    func roundedTime(from date: Date = Date()) -> Date {
        var components = calendar.dateComponents([.year, .month, .day, .hour, .minute], from: date)
        let minute = (components.minute ?? 0)
        components.minute = ((minute + 4) / 5) * 5
        return calendar.date(from: components) ?? date
    }
    
    func getHour(from date: Date) -> Int {
        return calendar.component(.hour, from: date)
    }
    
    func getMinute(from date: Date) -> Int {
        return calendar.component(.minute, from: date)
    }
    
    func validateTimeRange(startTime: Date, duration: TimeInterval, existingTasks: [Task]) -> Bool {
        let newRange = TimeRange(start: startTime, duration: duration)
        return !existingTasks.contains { task in
            guard let taskStart = task.startTime,
                  let taskDuration = task.duration else {
                return false
            }
            let taskRange = TimeRange(start: taskStart, duration: taskDuration)
            return newRange.overlaps(with: taskRange)
        }
    }
    
    func calculateDurationFromHeight(_ height: CGFloat, pixelsPerHour: CGFloat) -> TimeInterval {
        let durationHours = height / pixelsPerHour
        let totalSeconds = TimeInterval(durationHours) * TimeInterval(3600)
        // Round to nearest 15 minutes (900 seconds)
        return (totalSeconds / 900).rounded() * 900
    }
    
    // MARK: - Formatting
    private func formatter(for style: DateFormatStyle) -> DateFormatter {
        if let cached = formatters[style] {
            return cached
        }
        
        let formatter = DateFormatter()
        formatter.dateFormat = style.dateFormat
        formatter.timeZone = timeZone
        formatters[style] = formatter
        return formatter
    }
    
    func formatTime(_ date: Date) -> String {
        return formatter(for: .time).string(from: date)
    }
    
    func formatDay(_ date: Date) -> String {
        return formatter(for: .day).string(from: date)
    }
    
    func formatMonthForCalendar(_ date: Date) -> String {
        return formatter(for: .monthYear).string(from: date).capitalized
    }
    
    func formatHour(_ date: Date) -> String {
        return formatter(for: .hour).string(from: date)
    }
    
    func formatDuration(_ duration: TimeInterval) -> String {
        let hours = Int(duration) / 3600
        let minutes = Int(duration) % 3600 / 60
        
        if hours > 0 {
            return "\(hours)ч \(minutes)м"
        } else {
            return "\(minutes)м"
        }
    }
    
    func getShortWeekdaySymbols() -> [String] {
        return calendar.veryShortWeekdaySymbols
    }
    
    // MARK: - Predicate Helpers
    func createDateRangePredicate(for date: Date, keyPath: String) -> NSPredicate {
        let startOfDay = self.startOfDay(for: date)
        let endOfDay = self.endOfDay(for: date)
        return NSPredicate(format: "\(keyPath) >= %@ AND \(keyPath) <= %@", 
                         startOfDay as NSDate, 
                         endOfDay as NSDate)
    }
    
    func createTimeRangePredicate(for date: Date, keyPath: String) -> NSPredicate {
        return createDateRangePredicate(for: date, keyPath: keyPath)
    }
    
    // MARK: - Default Values
    func getDefaultDate() -> Date {
        return Date()
    }
    
    func getDefaultStartTime() -> Date {
        return roundedTime(from: Date())
    }
    
    func getDefaultDuration() -> TimeInterval {
        // Default duration of 30 minutes (1800 seconds)
        return 1800
    }
    
    // MARK: - Task Date Helpers
    func getTaskDate(task: Task?, defaultDate: Date? = nil) -> Date {
        if let taskDate = task?.date {
            return taskDate
        }
        return defaultDate ?? getDefaultDate()
    }
    
    func getTaskStartTime(task: Task?) -> Date {
        if let startTime = task?.startTime {
            return startTime
        }
        return getDefaultStartTime()
    }
    
    func calculateEndTime(startTime: Date, duration: TimeInterval) -> Date {
        return startTime.addingTimeInterval(duration)
    }
}