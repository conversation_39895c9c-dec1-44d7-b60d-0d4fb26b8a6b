import SwiftUI
import Combine

/// Контейнер зависимостей, использующий property wrappers для удобного доступа к зависимостям
final class DIContainer: ObservableObject {
    // MARK: - Shared Instance
    
    static let shared = DIContainer()
    
    // MARK: - Services
    
    /// Менеджер данных
    @Published var dataManager: DataManager
    
    /// Сервис работы с приоритетами задач
    let priorityService: TaskPriorityServiceProtocol
    
    /// Сервис работы с категориями
    let categoryService: CategoryServiceProtocol
    
    /// Сервис обратной связи
    @Published var feedbackService: FeedbackServiceProtocol?
    
    /// Сервис работы с датами и временем
    let dateTimeService: DateTimeServiceProtocol
    
    /// Сервис работы с задачами
    let taskService: TaskService
    
    /// ViewModel списка задач
    private(set) lazy var taskListViewModel: TaskListViewModel = {
        TaskListViewModel(container: self)
    }()
    
    // MARK: - Private Properties
    
    private var cancellables = Set<AnyCancellable>()

    // MARK: - Initialization
    
    init(dateTimeService: DateTimeServiceProtocol = StandardDateTimeService.shared,
         persistenceController: PersistenceController = PersistenceController.shared,
         priorityService: TaskPriorityServiceProtocol? = nil,
         categoryService: CategoryServiceProtocol = CategoryService()) {
        let dataManager = DataManager(persistenceController: persistenceController, dateTimeService: dateTimeService)
        self.dataManager = dataManager
        self.dateTimeService = dateTimeService
        self.priorityService = priorityService ?? TaskPriorityService(dateTimeService: dateTimeService)
        self.categoryService = categoryService
        self.taskService = TaskService(dataManager: dataManager)
        
        // FeedbackService will be initialized when needed via getFeedbackService
    }
    
    deinit {
        cancellables.forEach { $0.cancel() }
        cancellables.removeAll()
    }
    
    // MARK: - Task Management
    
    func addTask(_ task: Task) -> Bool {
        dataManager.saveTask(task)
    }
    
    /// Получает сервис обратной связи, создавая его при необходимости
    @MainActor
    private func getFeedbackService() -> FeedbackServiceProtocol? {
        if (feedbackService == nil) {
            feedbackService = FeedbackService()
        }
        return feedbackService
    }
}

// MARK: - Haptic Feedback Methods
extension DIContainer {
    /// Воспроизводит тактильную обратную связь указанного типа
    /// - Parameter type: Тип обратной связи (легкая, средняя и т.д.)
    @MainActor
    func playHaptic(_ type: HapticFeedbackType) {
        getFeedbackService()?.playHaptic(type)
    }
    
    /// Воспроизводит звуковую обратную связь для успешного действия
    @MainActor
    func playSuccessSound() {
        getFeedbackService()?.playSuccessSound()
    }
    
    /// Воспроизводит звуковую обратную связь для ошибки
    @MainActor
    func playErrorSound() {
        getFeedbackService()?.playErrorSound()
    }
}