import Foundation
import CoreData
import Combine

/// Сервис для управления задачами, централизующий всю логику работы с задачами
class TaskService {
    // MARK: - Properties
    
    private let dataManager: DataManager
    private var cancellables = Set<AnyCancellable>()
    
    /// Субъект для публикации событий изменения задач
    private let taskChangesSubject = PassthroughSubject<Void, Never>()
    
    /// Издатель для подписки на изменения задач
    var taskChanges: AnyPublisher<Void, Never> {
        taskChangesSubject.eraseToAnyPublisher()
    }
    
    // MARK: - Initialization
    
    init(dataManager: DataManager) {
        self.dataManager = dataManager
    }
    
    // MARK: - CRUD Operations
    
    /// Создание новой задачи с базовыми параметрами
    @discardableResult
    func createTask(title: String, priority: TaskPriority, date: Date = Date(), category: TaskCategory? = nil) -> Task {
        let task = Task(
            id: UUID(),
            title: title,
            description: nil,
            priority: priority,
            isCompleted: false,
            date: date,
            category: category
        )
        
        if saveTask(task) {
            return task
        } else {
            // Возвращаем задачу даже в случае ошибки, но логируем проблему
            print("Warning: Task created but not saved: \(task.title)")
            return task
        }
    }
    
    /// Создание задачи с расписанием
    @discardableResult
    func createScheduledTask(title: String, priority: TaskPriority, date: Date, startTime: Date, duration: TimeInterval, category: TaskCategory? = nil) -> Task {
        let task = Task(
            id: UUID(),
            title: title,
            description: nil,
            priority: priority,
            isCompleted: false,
            date: date,
            category: category,
            startTime: startTime,
            duration: duration
        )
        
        _ = saveTask(task)
        return task
    }
    
    /// Обновление существующей задачи с частичными параметрами
    @discardableResult
    func updateTask(_ task: Task, 
                    title: String? = nil, 
                    description: String? = nil,
                    priority: TaskPriority? = nil,
                    isCompleted: Bool? = nil,
                    date: Date? = nil,
                    category: TaskCategory? = nil) -> Bool {
        var updatedTask = task
        
        if let title = title { updatedTask.title = title }
        if let description = description { updatedTask.description = description }
        if let priority = priority { updatedTask.priority = priority }
        if let isCompleted = isCompleted { updatedTask.isCompleted = isCompleted }
        if let date = date { updatedTask.date = date }
        updatedTask.category = category // nil тоже валидное значение для сброса категории
        
        return saveTask(updatedTask)
    }
    
    /// Обновление расписания задачи
    @discardableResult
    func scheduleTask(_ task: Task, startTime: Date?, duration: TimeInterval?) -> Bool {
        var updatedTask = task
        updatedTask.startTime = startTime
        updatedTask.duration = duration
        
        return saveTask(updatedTask)
    }
    
    /// Сохранение задачи через DataManager с публикацией события изменения
    @discardableResult
    func saveTask(_ task: Task) -> Bool {
        let result = dataManager.saveTask(task)
        if result {
            taskChangesSubject.send()
        }
        return result
    }
    
    /// Удаление задачи
    @discardableResult
    func deleteTask(_ task: Task) -> Bool {
        let result = dataManager.deleteTask(task)
        if result {
            taskChangesSubject.send()
        }
        return result
    }
    
    // MARK: - Task State Management
    
    /// Переключение состояния выполнения задачи
    @discardableResult
    func toggleTaskCompletion(_ task: Task) -> Bool {
        var updatedTask = task
        updatedTask.isCompleted.toggle()
        return saveTask(updatedTask)
    }
    
    // MARK: - Query Methods
    
    /// Получение всех задач
    func fetchAllTasks() -> [Task] {
        dataManager.fetchAllTasks()
        return dataManager.tasks
    }
    
    /// Получение задач на определенную дату
    func fetchTasks(forDate date: Date? = nil, onlyScheduled: Bool = false) -> [Task] {
        if let date = date {
            dataManager.fetchTasks(for: date, onlyScheduled: onlyScheduled)
        } else {
            dataManager.fetchAllTasks()
        }
        return dataManager.tasks
    }
    
    /// Получение задач по приоритету
    func fetchTasksByPriority(_ priority: TaskPriority? = nil, forDate: Date? = nil) -> [Task] {
        if let priority = priority {
            if let date = forDate {
                // Используем DateTimeService через публичное API DataManager
                return dataManager.tasks.filter { task in 
                    task.priority == priority && 
                    areDatesInSameDay(task.date, date)
                }
            } else {
                return dataManager.fetchTasks(withPriority: priority)
            }
        }
        return dataManager.tasks
    }
    
    /// Получение задач по категории
    func fetchTasksByCategory(_ category: TaskCategory?, forDate: Date? = nil) -> [Task] {
        let allTasks = forDate != nil ? fetchTasks(forDate: forDate) : dataManager.tasks
        
        if let category = category {
            return allTasks.filter { $0.category?.id == category.id }
        } else {
            return allTasks.filter { $0.category == nil }
        }
    }
    
    /// Получение запланированных задач
    func fetchScheduledTasks(forDate date: Date) -> [Task] {
        return fetchTasks(forDate: date).filter { $0.isScheduled }
    }
    
    /// Проверка возможности добавления задачи с указанным приоритетом
    func canAddTask(with priority: TaskPriority, forDate date: Date) -> Bool {
        let existingTasks = fetchTasks(forDate: date)
        
        // Проверка максимального количества задач с данным приоритетом
        let tasksWithPriority = existingTasks.filter { task in
            task.priority == priority && 
            areDatesInSameDay(task.date, date)
        }
        
        return tasksWithPriority.count < priority.maxTasks
    }
    
    /// Проверка пересечения времени задачи с существующими задачами
    func validateTimeRange(startTime: Date, duration: TimeInterval, excludingTask: Task? = nil) -> Bool {
        let existingTasks = fetchScheduledTasks(forDate: startTime)
            .filter { task in excludingTask == nil || task.id != excludingTask!.id }
        
        return !hasOverlappingTasks(startTime: startTime, duration: duration, tasks: existingTasks)
    }
    
    // MARK: - Helper Methods
    
    /// Проверяет, находятся ли две даты в одном дне
    private func areDatesInSameDay(_ date1: Date, _ date2: Date) -> Bool {
        let calendar = Calendar.current
        return calendar.isDate(date1, inSameDayAs: date2)
    }
    
    /// Проверяет пересечение временных интервалов задач
    private func hasOverlappingTasks(startTime: Date, duration: TimeInterval, tasks: [Task]) -> Bool {
        let newRange = TimeRange(start: startTime, duration: duration)
        
        return tasks.contains { task in
            guard let taskStart = task.startTime,
                  let taskDuration = task.duration else {
                return false
            }
            
            let taskRange = TimeRange(start: taskStart, duration: taskDuration)
            return newRange.overlaps(with: taskRange)
        }
    }
}