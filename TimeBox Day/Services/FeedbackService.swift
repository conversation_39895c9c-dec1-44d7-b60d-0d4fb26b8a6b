import SwiftUI
import Combine

/// Типы тактильной обратной связи
public enum HapticFeedbackType {
    /// Легкое касание
    case light
    /// Среднее касание
    case medium
    /// Сильное касание
    case heavy
    /// Успешное действие
    case success
    /// Предупреждение
    case warning
    /// Ошибка
    case error
}

/// Протокол сервиса обратной связи
@MainActor
public protocol FeedbackServiceProtocol {
    /// Воспроизводит тактильную обратную связь указанного типа
    /// - Parameter type: Тип тактильной обратной связи
    func playHaptic(_ type: HapticFeedbackType)
    
    /// Воспроизводит звуковую обратную связь для успешного действия
    func playSuccessSound()
    
    /// Воспроизводит звуковую обратную связь для ошибки
    func playErrorSound()
}

/// Сервис для централизованного управления тактильной и звуковой обратной связью
@MainActor
public final class FeedbackService: FeedbackServiceProtocol, ObservableObject {
    // MARK: - Private Properties
    
    /// Настройки приложения
    private let appSettings: AppSettings
    
    /// Флаг, указывающий, включена ли тактильная обратная связь
    private let hapticFeedbackEnabled: Bool
    
    /// Генератор тактильной обратной связи для уведомлений
    private let notificationGenerator = UINotificationFeedbackGenerator()
    
    /// Генераторы тактильной обратной связи для различных интенсивностей
    private let impactGenerators: [UIImpactFeedbackGenerator.FeedbackStyle: UIImpactFeedbackGenerator]
    
    // MARK: - Initialization
    
    /// Инициализирует сервис обратной связи с настройками по умолчанию
    public init() {
        self.appSettings = AppSettings.shared
        self.hapticFeedbackEnabled = true
        
        // Инициализация генераторов для различных интенсивностей
        self.impactGenerators = [
            .light: UIImpactFeedbackGenerator(style: .light),
            .medium: UIImpactFeedbackGenerator(style: .medium),
            .heavy: UIImpactFeedbackGenerator(style: .heavy)
        ]
        
        // Подготовка генераторов
        self.notificationGenerator.prepare()
        self.impactGenerators.values.forEach { $0.prepare() }
    }
    
    /// Инициализирует сервис обратной связи
    /// - Parameters:
    ///   - appSettings: Настройки приложения
    ///   - hapticFeedbackEnabled: Флаг, указывающий, включена ли тактильная обратная связь
    init(appSettings: AppSettings, hapticFeedbackEnabled: Bool) {
        self.appSettings = appSettings
        self.hapticFeedbackEnabled = hapticFeedbackEnabled
        
        // Инициализация генераторов для различных интенсивностей
        self.impactGenerators = [
            .light: UIImpactFeedbackGenerator(style: .light),
            .medium: UIImpactFeedbackGenerator(style: .medium),
            .heavy: UIImpactFeedbackGenerator(style: .heavy)
        ]
        
        // Подготовка генераторов
        self.notificationGenerator.prepare()
        self.impactGenerators.values.forEach { $0.prepare() }
    }
    
    // MARK: - Public Methods
    
    public func playHaptic(_ type: HapticFeedbackType) {
        // Проверяем, включена ли тактильная обратная связь
        guard hapticFeedbackEnabled else { return }
        
        switch type {
        case .light, .medium, .heavy:
            playImpactFeedback(for: type)
        case .success, .warning, .error:
            playNotificationFeedback(for: type)
        }
    }
    
    public func playSuccessSound() {
        // Реализация воспроизведения звука успеха
        // Можно использовать AudioServicesPlaySystemSound или AVAudioPlayer
    }
    
    public func playErrorSound() {
        // Реализация воспроизведения звука ошибки
        // Можно использовать AudioServicesPlaySystemSound или AVAudioPlayer
    }
    
    // MARK: - Private Methods
    
    /// Воспроизводит тактильную обратную связь для импульсов различной интенсивности
    /// - Parameter type: Тип тактильной обратной связи
    private func playImpactFeedback(for type: HapticFeedbackType) {
        let style: UIImpactFeedbackGenerator.FeedbackStyle
        
        switch type {
        case .light:
            style = .light
        case .medium:
            style = .medium
        case .heavy:
            style = .heavy
        default:
            return
        }
        
        if let generator = impactGenerators[style] {
            generator.impactOccurred()
            generator.prepare() // Подготовка для следующего использования
        }
    }
    
    /// Воспроизводит тактильную обратную связь для уведомлений
    /// - Parameter type: Тип тактильной обратной связи
    private func playNotificationFeedback(for type: HapticFeedbackType) {
        let feedbackType: UINotificationFeedbackGenerator.FeedbackType
        
        switch type {
        case .success:
            feedbackType = .success
        case .warning:
            feedbackType = .warning
        case .error:
            feedbackType = .error
        default:
            return
        }
        
        notificationGenerator.notificationOccurred(feedbackType)
        notificationGenerator.prepare() // Подготовка для следующего использования
    }
}