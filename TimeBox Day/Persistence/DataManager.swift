import Foundation
import CoreData
import Combine
import SwiftUI

enum DataManagerError: Error {
    case saveFailed(String)
    case entityNotFound(String)
    case invalidData(String)
}

/// Оптимизированный менеджер данных, объединяющий функциональность DataManager и репозиториев
class DataManager: ObservableObject {
    // MARK: - Published Properties
    @Published private(set) var tasks: [Task] = []
    @Published private(set) var categories: [TaskCategory] = []
    @Published private(set) var isLoading = false
    @Published private(set) var error: Error?
    @Published private(set) var isReady = false
    
    // MARK: - Private Properties
    private let persistenceController: PersistenceController
    private let priorityService: TaskPriorityServiceProtocol
    private let dateTimeService: DateTimeServiceProtocol
    private var cancellables = Set<AnyCancellable>()
    
    // Subject для безопасного обновления состояния
    private let stateUpdateQueue = DispatchQueue(label: "com.timebox.stateupdate")
    private let stateUpdateSubject = PassthroughSubject<(() -> Void), Never>()
    
    // MARK: - Initialization
    init(persistenceController: PersistenceController = .shared,
         priorityService: TaskPriorityServiceProtocol? = nil,
         dateTimeService: DateTimeServiceProtocol = StandardDateTimeService.shared) {
        self.persistenceController = persistenceController
        self.dateTimeService = dateTimeService
        self.priorityService = priorityService ?? TaskPriorityService(dateTimeService: dateTimeService)
        
        setupBindings()
    }
    
    deinit {
        // Ensure we clean up all subscriptions
        cancellables.forEach { $0.cancel() }
        cancellables.removeAll()
    }
    
    // MARK: - Private Setup
    private func setupBindings() {
        // Подписка на готовность CoreData
        persistenceController.$isReady
            .receive(on: stateUpdateQueue)
            .sink { [weak self] isReady in
                guard let self = self else { return }
                self.updateState { self.isReady = isReady }
                
                if isReady {
                    self.fetchInitialData()
                }
            }
            .store(in: &cancellables)
        
        // Централизованная обработка обновлений состояния
        stateUpdateSubject
            .receive(on: stateUpdateQueue)
            .collect(.byTime(DispatchQueue.main, 0.1))
            .sink { [weak self] updates in
                guard let self = self else { return }
                DispatchQueue.main.async {
                    self.objectWillChange.send()
                    updates.forEach { $0() }
                }
            }
            .store(in: &cancellables)
    }
    
    private func fetchInitialData() {
        Just(())
            .delay(for: .milliseconds(100), scheduler: stateUpdateQueue)
            .sink { [weak self] _ in
                self?.fetchAllCategories()
                self?.fetchAllTasks()
            }
            .store(in: &cancellables)
    }
    
    private func updateState(_ update: @escaping () -> Void) {
        stateUpdateSubject.send(update)
    }
    
    // MARK: - Category Operations
    
    /// Получение всех категорий
    func fetchAllCategories() {
        guard persistenceController.isReady else { return }
        updateState { self.isLoading = true }
        
        let context = persistenceController.container.viewContext
        let fetchRequest = NSFetchRequest<Category>(entityName: "Category")
        let sortDescriptors = [NSSortDescriptor(key: "name", ascending: true)]
        fetchRequest.sortDescriptors = sortDescriptors
        
        context.perform { [weak self] in
            guard let self = self else { return }
            do {
                let entities = try context.fetch(fetchRequest)
                let newCategories = entities.compactMap { self.toCategoryModel($0) }
                
                self.updateState {
                    self.categories = newCategories
                    self.isLoading = false
                }
            } catch {
                self.handleError(error, operation: "fetchAllCategories")
            }
        }
    }
    
    /// Сохранение категории
    @discardableResult
    func saveCategory(_ category: TaskCategory) -> Result<TaskCategory, DataManagerError> {
        guard persistenceController.isReady else { 
            return .failure(.saveFailed("Core Data not ready")) 
        }
        
        let context = persistenceController.container.viewContext
        
        if let existingCategory = fetchCategoryEntity(withID: category.id) {
            updateCategoryEntity(existingCategory, from: category)
        } else {
            guard let entityDescription = NSEntityDescription.entity(forEntityName: "Category", in: context) else {
                return .failure(.invalidData("Failed to create Category entity"))
            }
            
            let newCategory = Category(entity: entityDescription, insertInto: context)
            updateCategoryEntity(newCategory, from: category)
        }
        
        return saveContext() ? .success(category) : .failure(.saveFailed("Failed to save category"))
    }
    
    /// Удаление категории
    @discardableResult
    func deleteCategory(withID id: UUID) -> Bool {
        guard let existingCategory = fetchCategoryEntity(withID: id) else { return false }
        
        let context = persistenceController.container.viewContext
        context.delete(existingCategory)
        
        return saveContext()
    }

    // MARK: - Task Operations
    
    /// Получение всех задач
    func fetchAllTasks() {
        guard persistenceController.isReady else { return }
        updateState { self.isLoading = true }
        
        let context = persistenceController.container.viewContext
        let fetchRequest = NSFetchRequest<TaskEntity>(entityName: "TaskEntity")
        
        context.perform { [weak self] in
            guard let self = self else { return }
            
            do {
                let entities = try context.fetch(fetchRequest)
                let newTasks = entities.compactMap { self.toTaskModel($0) }
                
                self.updateState {
                    self.tasks = newTasks
                    self.isLoading = false
                }
            } catch {
                self.handleError(error, operation: "fetchAllTasks")
            }
        }
    }
    
    /// Получение задач для конкретной даты
    func fetchTasks(for date: Date, onlyScheduled: Bool = false) {
        guard persistenceController.isReady else { return }
        
        let context = persistenceController.container.viewContext
        let fetchRequest = NSFetchRequest<TaskEntity>(entityName: "TaskEntity")
        
        // Создаем предикат для фильтрации по дате
        fetchRequest.predicate = onlyScheduled
            ? dateTimeService.createTimeRangePredicate(for: date, keyPath: "startTime")
            : dateTimeService.createDateRangePredicate(for: date, keyPath: "date")
        
        if onlyScheduled {
            fetchRequest.sortDescriptors = [NSSortDescriptor(key: "startTime", ascending: true)]
        }
        
        do {
            let entities = try context.fetch(fetchRequest)
            let newTasks = entities.compactMap { self.toTaskModel($0) }
            
            DispatchQueue.main.async { [weak self] in
                self?.objectWillChange.send()
                self?.tasks = newTasks
                self?.isLoading = false
            }
        } catch {
            self.handleError(error, operation: "fetchTasks(for:)")
        }
    }
    
    /// Получение задач по приоритету с опциональной фильтрацией по дате
    func fetchTasks(withPriority priority: TaskPriority, for date: Date? = nil) -> [Task] {
        guard persistenceController.isReady else { return [] }
        
        let context = persistenceController.container.viewContext
        let fetchRequest = NSFetchRequest<TaskEntity>(entityName: "TaskEntity")
        
        var predicates: [NSPredicate] = [NSPredicate(format: "priority == %d", priority.rawValue)]
        
        if let date = date {
            predicates.append(dateTimeService.createDateRangePredicate(for: date, keyPath: "date"))
        }
        
        fetchRequest.predicate = NSCompoundPredicate(andPredicateWithSubpredicates: predicates)
        
        do {
            let entities = try context.fetch(fetchRequest)
            return entities.compactMap { toTaskModel($0) }
        } catch {
            handleError(error, operation: "fetchTasks(withPriority:for:)")
            return []
        }
    }
    
    /// Сохранение задачи (новой или существующей)
    @discardableResult
    func saveTask(_ task: Task) -> Bool {
        guard persistenceController.isReady else { return false }
        
        // Получаем задачи с той же датой и проверяем приоритеты
        let tasksForDate = fetchTasks(withPriority: task.priority, for: task.date)
            .filter { $0.id != task.id && !$0.isCompleted } // Исключаем саму задачу и завершенные
        
        guard priorityService.validateTaskPriority(task, existingTasks: tasksForDate) else {
            return false
        }
        
        let context = persistenceController.container.viewContext
        
        if let existingTask = fetchTaskEntity(withID: task.id) {
            updateTaskEntity(existingTask, from: task)
        } else {
            guard let entityDescription = NSEntityDescription.entity(forEntityName: "TaskEntity", in: context) else {
                handleError(DataManagerError.invalidData("Failed to create TaskEntity"), operation: "saveTask")
                return false
            }
            
            let newTask = TaskEntity(entity: entityDescription, insertInto: context)
            updateTaskEntity(newTask, from: task)
        }
        
        return saveContext()
    }
    
    /// Удаление задачи
    @discardableResult
    func deleteTask(_ task: Task) -> Bool {
        guard let existingTask = fetchTaskEntity(withID: task.id) else { return false }
        
        let context = persistenceController.container.viewContext
        context.delete(existingTask)
        
        return saveContext()
    }

    // MARK: - Entity Fetch Helpers
    
    /// Получение сущности задачи по ID
    private func fetchTaskEntity(withID id: UUID) -> TaskEntity? {
        guard persistenceController.isReady else { return nil }
        
        let context = persistenceController.container.viewContext
        let fetchRequest = NSFetchRequest<TaskEntity>(entityName: "TaskEntity")
        fetchRequest.predicate = NSPredicate(format: "id == %@", id as CVarArg)
        fetchRequest.fetchLimit = 1
        
        do {
            return try context.fetch(fetchRequest).first
        } catch {
            self.error = error
            return nil
        }
    }
    
    /// Получение сущности категории по ID
    private func fetchCategoryEntity(withID id: UUID) -> Category? {
        guard persistenceController.isReady else { return nil }
        
        let context = persistenceController.container.viewContext
        let fetchRequest = NSFetchRequest<Category>(entityName: "Category")
        fetchRequest.predicate = NSPredicate(format: "id == %@", id as CVarArg)
        fetchRequest.fetchLimit = 1
        
        do {
            return try context.fetch(fetchRequest).first
        } catch {
            self.error = error
            return nil
        }
    }
    
    // MARK: - Entity Update Helpers
    
    /// Обновление сущности задачи из модели
    private func updateTaskEntity(_ entity: TaskEntity, from model: Task) {
        entity.id = model.id
        entity.title = model.title
        entity.taskDescription = model.description
        entity.priority = Int16(model.priority.rawValue)
        entity.isCompleted = model.isCompleted
        entity.date = model.date
        
        // Поля для временного планирования
        entity.startTime = model.startTime
        if let duration = model.duration {
            entity.duration = NSNumber(value: duration)
        } else {
            entity.duration = nil
        }
    }
    
    /// Обновление сущности категории из модели
    private func updateCategoryEntity(_ entity: Category, from model: TaskCategory) {
        entity.id = model.id
        entity.name = model.name
        // Преобразуем Color в строковое представление для хранения
        if model.color == .blue {
            entity.colorHex = "blue"
        } else if model.color == .red {
            entity.colorHex = "red"
        } else if model.color == .green {
            entity.colorHex = "green"
        } else {
            entity.colorHex = "blue" // значение по умолчанию
        }
    }
    
    // MARK: - Model Conversion Helpers
    
    /// Конвертация сущности задачи в модель
    private func toTaskModel(_ entity: TaskEntity) -> Task? {
        return Task(
            id: entity.id,
            title: entity.title,
            description: entity.taskDescription,
            priority: TaskPriority(rawValue: Int(entity.priority)) ?? .tasks,
            isCompleted: entity.isCompleted,
            date: entity.date,
            category: entity.category.flatMap(toCategoryModel),
            startTime: entity.startTime,
            duration: entity.duration?.doubleValue
        )
    }
    
    /// Конвертация сущности категории в модель
    private func toCategoryModel(_ entity: Category) -> TaskCategory? {
        // Преобразуем строковое представление обратно в Color
        let color: Color = switch entity.colorHex {
            case "red": .red
            case "green": .green
            default: .blue
        }
        
        return TaskCategory(
            id: entity.id,
            name: entity.name,
            color: color
        )
    }
    
    // MARK: - Utility Methods
    
    /// Сохранение изменений контекста
    @discardableResult
    func saveContext() -> Bool {
        let context = persistenceController.container.viewContext
        guard context.hasChanges else { return true }
        
        do {
            try context.save()
            updateState { }  // Триггерим обновление UI
            return true
        } catch {
            updateState { self.error = error }
            context.rollback()
            return false
        }
    }
    
    /// Сброс изменений контекста
    func rollbackContext() {
        let context = persistenceController.container.viewContext
        context.rollback()
    }
    
    /// Обработка ошибок
    private func handleError(_ error: Error, operation: String) {
        updateState {
            self.error = error
            self.isLoading = false
        }
        print("Failed to \(operation): \(error)")
    }
}