import CoreData
import Foundation
import SwiftUI
import Combine

class PersistenceController: ObservableObject {
    static let shared = PersistenceController()
    
    let container: NSPersistentContainer
    @Published var isReady: Bool = false
    @Published var loadError: String? = nil
    
    // Storage for Combine subscriptions
    internal var cancellables = Set<AnyCancellable>()
    
    init(inMemory: Bool = false) {
        // Create container
        container = NSPersistentContainer(name: "TimeBoxModel")
        
        if inMemory {
            container.persistentStoreDescriptions.first?.url = URL(fileURLWithPath: "/dev/null")
        }
        
        // Configure persistent store
        if let description = container.persistentStoreDescriptions.first {
            description.setOption(true as NSNumber, forKey: NSPersistentStoreRemoteChangeNotificationPostOptionKey)
            description.setOption(true as NSNumber, forKey: NSPersistentHistoryTrackingKey)
        }
        
        // Load persistent stores on background queue
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self = self else { return }
            
            self.container.loadPersistentStores { (storeDescription, error) in
                DispatchQueue.main.async {
                    if let error = error as NSError? {
                        self.loadError = self.handlePersistentStoreError(error)
                        // Debug print removed
                    } else {
                        // Debug print removed
                        self.configureContext()
                        self.isReady = true
                    }
                }
            }
        }
    }
    
    private func configureContext() {
        container.viewContext.automaticallyMergesChangesFromParent = true
        container.viewContext.mergePolicy = NSMergeByPropertyObjectTrumpMergePolicy
        
        // Enable persistent history tracking
        container.viewContext.transactionAuthor = "app"
        
        // Configure undo management
        container.viewContext.undoManager = nil
        
        // Set fetch batch size
        container.viewContext.shouldDeleteInaccessibleFaults = true
    }
    
    private func handlePersistentStoreError(_ error: NSError) -> String {
        // Check if this is a migration error
        if error.code == NSPersistentStoreIncompatibleVersionHashError ||
           error.code == NSMigrationMissingSourceModelError {
            return "Database version mismatch. Please reinstall the app."
        }
        
        // Check if this is a permission error
        if error.code == NSPersistentStoreIncompatibleVersionHashError {
            return "Unable to access the database. Please check app permissions."
        }
        
        return "Failed to initialize database: \(error.localizedDescription)"
    }
    
    // Helper for SwiftUI previews
    static var preview: PersistenceController = {
        let controller = PersistenceController(inMemory: true)
        return controller
    }()
}
