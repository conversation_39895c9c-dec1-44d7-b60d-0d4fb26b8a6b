import Foundation
import CoreData

@objc(TaskEntity)
class TaskEntity: NSManagedObject {
    @NSManaged var id: UUID
    @NSManaged var title: String
    @NSManaged var taskDescription: String?
    @NSManaged var priority: Int16
    @NSManaged var isCompleted: Bool
    @NSManaged var date: Date
    @NSManaged var createdAt: Date?
    @NSManaged var category: Category?
    
    // Новые поля для временного планирования
    @NSManaged var startTime: Date?
    @NSManaged var duration: NSNumber?
    
    // Вычисляемое свойство для проверки, запланирована ли задача
    var isScheduled: Bool {
        return startTime != nil && duration != nil
    }
    
    // Вычисляемое свойство для конца временного интервала
    var endTime: Date? {
        guard let start = startTime, let durationValue = duration?.doubleValue else { return nil }
        return start.addingTimeInterval(durationValue)
    }
}
