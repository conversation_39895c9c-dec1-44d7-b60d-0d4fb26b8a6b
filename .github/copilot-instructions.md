# TimeBox Day Project Guidelines

## Главные принципы разработки
Все аспекты работы над проектом подчиняются трем базовым принципам, применяемым строго в указанном порядке:
1. **Убрать лишнее** - сначала удаляем ненужное, избыточное и устаревшее
2. **Исправить/улучшить существующее** - консолидируем, упрощаем, оптимизируем то, что остается после чистки
3. **Добавить недостающее** - только после первых двух шагов создаем новые элементы

## 1. Технический стек и коммуникация

### Требуемые технологии
- iOS 16.0+
- Swift 5.9+
- SwiftUI для UI компонентов
- Core Data для локального хранения
- Combine для управления состояниями

### Коммуникация
- Вся коммуникация ведется на русском языке

## 2. Методология работы с кодом

### Применение главных принципов
1. **Убираем лишнее:**
   - Удаляем дублирующийся код
   - Полностью удаляем устаревшую логику при внедрении новых подходов
   - Избавляемся от ненужных зависимостей и библиотек
   
2. **Исправляем/улучшаем:**
   - Упрощаем код вместо его расширения (все гениальное - просто)
   - При исправлении багов используем возможности текущей реализации
   - Оптимизируем существующие решения перед добавлением новых

3. **Добавляем недостающее:**
   - Новые компоненты создаем только при доказанной необходимости
   - Работаем только с релевантным для задачи кодом
   - Добавляем новую функциональность только после одобрения

### Процесс внесения изменений
- Перед модификацией кода:
  1. Четко определи проблему с учетом всего контекста
  2. Предложи оптимальное решение с обоснованием
  3. Дождись явного подтверждения от пользователя
- Структурные изменения только после прямого одобрения

## 3. UI принципы и стилизация

1. **Убираем лишнее:**
   - Отказ от избыточных UI элементов
   - Не используем устаревшие технологии (Storyboards, UIKit), где можно применить SwiftUI

2. **Исправляем/улучшаем:**
   - Строго соблюдаем системные стили и HIG
   - Используем стандартные компоненты с минимальной кастомизацией

3. **Добавляем недостающее:**
   - Создавай современный простой и красивый интерфейс
   - Создаем собственные компоненты только при отсутствии системных аналогов
   - Новые UI элементы должны органично вписываться в существующий дизайн

### Стандарты оформления
   - Стандартные шрифты (`.largeTitle`, `.title`, `.body`)
   - Стандартные отступы и пространства вместо ручных значений и фиксированных размеров
   - Использование системных констант для отступов (`.padding()` без параметров)
   - Применение динамических размеров с учетом адаптивности интерфейса (например, .frame(maxWidth: .infinity) )

## 4. Документация и управление проектом
   - Следуй плану в TODO.md или рефактор.md с отметкой выполненных задач
   - Придерживайся структуры проекта из README.md
   - Избегай избыточного документирования (отдельные README для компонентов)

## 5. Операционные процедуры

### Сборка и запуск
```bash
# Сборка
xcodebuild -scheme "TimeBox Day" -destination 'platform=iOS Simulator,name=iPhone 16 Pro' -configuration Debug build

# Запуск
xcrun simctl boot "iPhone 16 Pro" && xcrun simctl launch booted name.aleksei.svezhevskii.TimeBox-Day
```

### Тестирование
- Запускай тесты только по запросу пользователя
- Тестируй только системы, с которыми велась работа:
```bash
cd '/Users/<USER>/Desktop/MyApps/TimeBox\ Day' && xcodebuild test -scheme "TimeBox Day" -destination "platform=iOS Simulator,name=iPhone 16 Pro,OS=18.2" -only-testing "TimeBox DayTests/TaskPriorityTests"
