# TimeBox Приложение-Планировщик

Цель приложения: помочь человеку улучшить свою жизнь через повышение эффективности действий

## Задачи
- Детально описать порядок действий пользователя
- Сделать детальное описание экранов и поведение элементов
- Определить идеальную архитектуру приложения
- Написать код приложения

## Порядок действий пользователя
### Окно Задачи/Инбокс
- Разгружает голову:
  - Вносит все задачи из головы в инбокс:
    - большая иконка + в левом нижнем углу экрана

- Работает с общим списком задач:
  - Сортирует порядок задач перетаскиванием
  - Может добавить/отредактировать детали задачи, кликнув на задачу
    - описание
    - категорию/ярлык
    - добавить чек-лист
  - Отправляет задачи на выполнение на сегодня или будущую дату

### Планирует день:
  - Может создать задачу нажав + в пустой ячейке Progress Task или + в разделе Tasks
  - Или может установить главную задачу дня перетащив задачу из общего списка
  - Видит расписание дня с мероприятиями из синхронизированного календаря
  - Перетягивает задачи в расписание дня
  - Может передвигать задачу по расписанию дня длительным нажатием и перетягиванием
  - Может менять длительность задачи растягивая начало или конец задачи
  - Может передвигать мероприятия из синхронизированного календаря
  - Может менять длительность мероприятия из синхронизированного календаря
  
### Выполняет задачи:
  - Может отметить задачу выполенной в списке задач дня
  - Кликает на задачу в расписание и входит в режим фокуса (помодоро)
  - В режиме фокуса может отметить задачу выполненной, тогда конец задачи автоматически фиксируется и меняется длительность задачи в расписании дня

## ❓Вопросы:
- Что случается с задачей в инбоксе, когда ей назначена дата?
- **Решение** Переносится в раздел Запланировано?

- Как быть, если задача была вставлена в расписание, по ней запускался таймер, т.е. работы была, но задача не была завершена? Так может быть часто, и это должно считаться достижением, но должно быть видно, что задача не завершена. Есть ли какая-то иконка частично завершенный чек-бокс?
 - **Решение** Использование смешанного состояния чекбокса
SwiftUI поддерживает три состояния для Toggle с чекбокс-стилем: .off - задача не выполнена, .on - задача полностью выполнена, .mixed - задача частично выполнена: минус в кружочке

- Что происходит с невыполненными дневными задачами? Они возвращаются в инбокс? Или остатся в дне, чтобы видеть, что задача была назначена и невыполоена? Например Х или <, чтобы показать, что задача вернулась в инбокс

## Основной функционал
- Внесение задач в инбокс
- Выбор задач на день из инбокса
- Добавление задач непосредственно из дня
- Распределение задач по приоритетам
- Перетягиваине задач в расписание дня
- Изменение длительности задачи в расписании перетягиванием
- Просмотр синхронизированных мероприятий из календарей

## Структура интерфейса
Приложение имеет однооконный интерфейс, разделенный на три основные секции:

1. **Полоса календаря** (Верх)
   - Текущий месяц и год
     - при клике на этот заголовок, горизонтальная полоса, показывающая дни недели разворачивается и отображает месяц, при повторном клике сворачивается обратно
   - Горизонтальная полоса, показывающая дни недели
   - Текущий день выделен
   - Возможность пролистывать недели горизонтально и месяцы вертикально
   - Нажатие на день показывает задачи и расписание на этот день
   - Дни в прошлом доступны только для просмотра, но не для редактирования

2. **Список задач** (Левая колонка)
   - Разделение на 3 категории приоритета:
     * **Progress Task** (главная задача дня)
     * **Top-3** (три важнейшие задачи)
     * **Other** (остальные задачи)
   - Возможность перетаскивания задач между разделами для изменения приоритета
   - Галочки для отметки выполнения
   - Добавление новой задачи происходит через клик на пустой слот в категории
   - Функциональность перетаскивания задачи в расписание дня
   - Редактирование задач доступно только для текущего или будущих дней
   - Просмотр задач для прошедших дней без возможности редактирования

3. **Расписание дня** (Правая колонка)
   - Временная сетка дня
   - Отображение существующих событий из календаря
   - Отображение добавленных задач из списка задач
   - Возможность добавления задачи через клик на пустое время
   - Возможность регулировать продолжительность задач путем растягивания или сжатия блока задачи
   - Редактирование расписания доступно только для текущего или будущих дней
   - Просмотр расписания для прошедших дней без возможности редактирования

## Основной функционал
- Просмотр задач и расписаний по всем дням (прошлым, текущему и будущим)
- Создание, редактирование и удаление задач только для текущего и будущих дней
- Отметка выполнения задач возможна только для текущего дня
- Приоритизация задач с помощью групп "Progress Task", "Top-3" и "Other"
- Перетаскивание задач между группами приоритетов (только для текущего и будущих дней)
- Перетаскивание задач из списка в расписание (только для текущего и будущих дней)
- Интеграция с iOS и Google Календарями

## Дизайн приложения
- Полное соответствие Apple Human Interface Guidelines (HIG)
- Использование системных компонентов и стилей iOS
- Адаптация к системной теме (светлая/темная)
- Естественная интеграция с системными жестами и навигацией
- Соблюдение типографики iOS для оптимальной читаемости
- Приложение воспринимается как естественное продолжение операционной системы
- Плавные анимации и переходы в соответствии с iOS стандартами
- Использование системных иконок для интуитивно понятного интерфейса
- Полная поддержка функций доступности iOS (VoiceOver, Dynamic Type, уменьшение движения и т.д.)
- Цветовая палитра с учетом дальтонизма и высоким контрастом где необходимо


## Технические требования

### Платформа и совместимость
- **Минимальная версия iOS**: 16.0+
- **Устройства**: iPhone и iPad с поддержкой iOS 16.0 или новее
- **Ориентация**: Вертикальная для iPhone, вертикальная и горизонтальная для iPad

### Технологии разработки
- **Язык программирования**: Swift 5.9+
- **Фреймворк UI**: SwiftUI
- **Архитектура**: MVVM (Model-View-ViewModel)
- **Управление состоянием**: Combine framework
- **Локальное хранилище данных**: Core Data
- **Синхронизация с облаком**: CloudKit для пользовательских задач

### Интеграции
- **Календари**:
  - EventKit для интеграции с iOS Calendar
  - Google Calendar API для работы с Google Calendar
- **Уведомления**: UserNotifications framework для напоминаний
- **Виджеты**: WidgetKit для отображения задач дня на экране «Домой»

### Производительность
- **Время запуска**: Не более 2 секунд
- **Отзывчивость интерфейса**: Реакция на действия пользователя не более 0.1 секунды
- **Плавность анимаций**: 60 FPS для всех переходов и анимаций
- **Потребление памяти**: Не более 200 MB в активном состоянии

### Безопасность
- **Защита данных**: Шифрование локальных данных
- **Аутентификация**: Поддержка Face ID/Touch ID для быстрого доступа
- **Права доступа**: Явный запрос прав на доступ к календарю и уведомлениям

### Тестирование и обеспечение качества
- **Юнит-тестирование**: Покрытие > 80% бизнес-логики
- **UI-тестирование**: Автоматизированное тестирование основных пользовательских сценариев
- **Тестирование доступности**: Проверка совместимости с VoiceOver и другими вспомогательными технологиями
- **Бета-тестирование**: Через TestFlight с группами реальных пользователей
- **Мониторинг производительности**: Использование инструментов для отслеживания производительности и сбоев

## Структура приложения
```
TimeBox Day/
├── App/                              # Корневые компоненты приложения
│   ├── AppState.swift               # Глобальное состояние приложения
│   ├── ContentView.swift            # Корневое представление
│   ├── TimeBox_DayApp.swift         # Точка входа
│   └── TimeBoxModel.xcdatamodeld   # Модель Core Data
│
├── Common/                           # Общие компоненты
│   ├── CategoryBadgeView.swift      # Бейдж категории
│   ├── EmptyStateView.swift         # Отображение пустого состояния
│   ├── ErrorView.swift              # Отображение ошибок
│   ├── LoadingView.swift            # Индикатор загрузки
│   ├── TouchTargetModifiers.swift   # Модификаторы для таргетов
│   └── ViewModifiers.swift          # Общие модификаторы представлений
│
├── Config/                           # Конфигурация
│   └── AppSettings.swift            # Настройки приложения
│
├── Features/                         # Основные функциональные модули
│   ├── Base/                        # Базовые компоненты
│   │   └── BaseViewModel.swift      # Базовая модель представления
│   │
│   ├── Calendar/                    # Модуль календаря
│   │   ├── CalendarView.swift
│   │   ├── CalendarViewModel.swift
│   │   ├── DailyScheduleView.swift
│   │   └── DailyScheduleViewModel.swift
│   │
│   ├── Categories/                  # Модуль категорий
│   │   ├── CategoriesService.swift
│   │   ├── CategoriesView.swift
│   │   └── CategoriesViewModel.swift
│   │
│   ├── Navigation/                  # Модуль навигации
│   │   ├── Coordinator.swift
│   │   ├── MainTabView.swift
│   │   └── TabCoordinator.swift
│   │
│   ├── Settings/                    # Модуль настроек
│   │   └── SettingsView.swift
│   │
│   └── Tasks/                       # Модуль задач
│       ├── TaskListView.swift
│       ├── TaskListViewModel.swift
│       └── TaskView.swift
│
├── Models/                          # Модели данных
│   ├── Extensions/                  # Расширения
│   │   ├── Color+Extensions.swift
│   │   └── CombineOptimizer.swift
│   ├── Task.swift
│   ├── TaskCategory.swift
│   ├── TaskPriority.swift
│   └── TimeRange.swift
│
├── Persistence/                     # Слой хранения
│   ├── CategoryEntity.swift         # Сущность категории
│   ├── DataManager.swift            # Менеджер данных
│   ├── PersistenceController.swift  # Контроллер Core Data
│   └── TaskEntity.swift            # Сущность задачи
│
├── Resources/                       # Ресурсы приложения
│   └── Assets.xcassets             # Медиа-ресурсы
│
└── Services/                        # Сервисный слой
    ├── DateTimeService.swift        # Сервис дат и времени
    ├── DIContainer.swift           # Контейнер зависимостей
    ├── FeedbackService.swift       # Сервис обратной связи
    └── TaskService.swift           # Сервис задач
```

## Планы развития приложения

### Версия 1.1
- **Инбокс** - простой список задач без привязки к дате:
  - Быстрое добавление задач, чтобы не забыть
  - Перемещение задач из Инбокса в список задач на конкретный день
  - Доступ к Инбоксу через отдельную вкладку или свайп с левого края экрана
- **Расширенное взаимодействие с задачами**:
  - При клике на задачу в календаре - встроенный таймер в стиле Помодоро для работы над задачей
- **Интеграция с Google Календарем**:
  - Возможность просмотра и взаимодействия с событиями из Google Календаря
  - Синхронизация в обоих направлениях
- **Уведомления**:
  - Настраиваемые напоминания о задачах
  - Уведомления о завершении сессий Pomodoro
  - Гибкие настройки частоты и типов уведомлений

### Версия 2.0
- Статистика выполнения задач
  - Общее количество выполненных главных задач (Progress Task)
  - Текущий стрик (последовательность дней с выполненной главной задачей)
  - Процент выполнения задач по категориям
  - Наиболее продуктивное время дня
- Аналитика продуктивности
- **Расширенные функции Помодоро**:
  - Настройка длительности рабочих интервалов и перерывов
  - Статистика по использованию техники Помодоро
  - Интеграция статистики Помодоро с общей аналитикой продуктивности

### Версия 2.5
- **Аналитический модуль**:
  - Отслеживание выполнения задач
  - Статистика использования времени
  - Рекомендации на основе данных об эффективности
  - Анализ эффективности работы с использованием техники Помодоро
- **Начальное обучение (onboarding)**:
  - Поэтапное обучение с пояснением ключевых концепций приложения
  - Интерактивные подсказки при первом использовании определенных функций
  - Короткие обучающие видео для демонстрации основных возможностей
  - Возможность пропуска обучения и повторного доступа к обучающим материалам

## Особенности интерфейса

### Просмотр деталей задачи
Согласно принципам Human Interface Guidelines (HIG) и минимизации когнитивной нагрузки:
- Детали задачи отображаются в виде частичного листа (sheet), который появляется снизу экрана
- Сохраняется контекст основного экрана, так как лист не занимает всю площадь
- Закрытие производится интуитивным жестом свайпа вниз
- Информация организована в иерархическом порядке:
  - Название задачи наиболее заметно
  - Ключевые детали (дата, приоритет) отображаются непосредственно под названием
  - Описание и дополнительные детали располагаются ниже
  - Действия (редактирование, удаление, запуск таймера) сгруппированы логически

### Таймер Pomodoro
Реализация таймера Pomodoro создаёт среду максимальной концентрации внимания:
- При входе в режим Pomodoro пользователь видит полностью отдельный экран, содержащий только:
  - Название задачи
  - Описание задачи
  - Большой и чёткий таймер Pomodoro
- Фокусное состояние без отвлекающих факторов:
  - Отсутствие других элементов интерфейса для минимизации когнитивной нагрузки
  - Нейтральный фон, способствующий концентрации
  - Возможность выхода из режима одним жестом или кнопкой
- Визуальная обратная связь:
  - Чёткое визуальное различие между рабочими периодами и перерывами
  - Круговая визуализация прогресса
  - Плавные анимации для изменения состояния
  - Тактильная обратная связь (вибрация) при переходе между периодами
- Минимальные элементы управления:
  - Простая кнопка воспроизведения/паузы
  - Возможность пропустить период
  - Опция сброса

## Метрики успеха приложения
- **Вовлеченность пользователей**: Ежедневное активное использование > 60% установивших
- **Удержание пользователей**: 30-дневное удержание > 40%
- **Выполнение задач**: Процент выполненных задач > 70%
- **Пользовательская удовлетворенность**: Рейтинг в App Store > 4.5
- **Скорость освоения**: Среднее время до создания первой задачи < 2 минуты