# План пошагового рефакторинга TimeBox Day

## Исследование перед рефактором

### Анализ кода
- [ ] проанализируй, что мы можем еще безопасно почистить
- [ ] проанализируй структуру проекта и весь код (игнорируй документы) согласно главным принципам разработки и определи самую главную проблему, решив которую остальные проблемы уменьшатся или вообще исчезнут
- [ ] Выяви дублирующийся код/функции
- [ ] Найди файлы длиннее 200 строк
- [ ] Проведи Code Review критических компонентов
- [ ] Проверь наличие неиспользуемого кода, файлов и ресурсов в проекте
- [ ] Проверь корректность управления жизненным циклом объектов
- [ ] Проверь обработку ошибок и исключений
- [ ] Изучи соблюдение принципов SOLID в архитектуре
- [ ] Проанализируй структуру и код проекта, чтобы оценить, нет ли в нём излишней сложности.
- [ ] Проанализируй структуру папок и расположение файлов проекта и оцени, нужна ли реорганизация.
- [ ] Проанализирую наименования всех файлов в проекте на предмет консистентности согласно их функции.
- [ ] Проанализируй избыточность архитектуры (лишние слои абстракции, переусложнённые паттерны, избыточное делегирование)

Не изменять выше этой линии
____


### Упрощение систем стилизации
- [ ] Оптимизировать размеры UI:
  * Заменить кастомные константы на системные значения
  * Использовать динамические шрифты вместо фиксированных размеров
  * Упростить CalendarLayout, ListLayout, BadgeLayout

Валидация: Проверка на всех экранах, что UI не сломался

## Фаза 2: Улучшение организации (2-3 дня)

### 2.1. Стандартизация имен файлов
- [ ] Согласовать префиксы в именах файлов:
  * Экраны: `FeatureNameView.swift` и `FeatureNameViewModel.swift`
  * Общие компоненты: `TBDComponentNameView.swift`
  * Тесты: `FeatureNameTests.swift`
- [ ] Привести к единому стилю названия View и ViewModel:
  * Переименовать ContentView в MainView
  * Добавить суффикс View ко всем SwiftUI views
  * Создать ViewModels для всех основных View
- [ ] Обновить названия тестовых файлов:
  * Использовать суффикс Tests для всех тестовых файлов
  * Группировать тесты по фичам
Валидация: Успешная компиляция проекта

### 2.2. Базовая реорганизация папок
- [ ] Создать четкую структуру в Features/
- [ ] Перегруппировать связанные View и ViewModel
- [ ] Организовать Common компоненты
Валидация: Проект собирается и запускается

### 2.3. Проверка структуры сервисов
- [ ] Аудит DIContainer
- [ ] Упростить инициализацию сервисов
- [ ] Проверить все зависимости
Валидация: Все сервисы работают корректно

## Фаза 3: Оптимизация кода (3-4 дня)

### 3.1. Улучшение TaskService
- [ ] Объединить дублирующуюся логику
- [ ] Оптимизировать работу с Core Data
- [ ] Улучшить обработку ошибок
Валидация: Все операции с задачами работают корректно

### 3.2. Упрощение ViewModels
- [ ] Уменьшить количество @Published свойств
- [ ] Оптимизировать биндинги
- [ ] Упростить логику обновления состояния
Валидация: UI корректно реагирует на изменения

### 3.3. Оптимизация UI компонентов
- [ ] Стандартизировать ViewModifier-ы
- [ ] Улучшить переиспользование компонентов
- [ ] Оптимизировать иерархию View
Валидация: Все экраны работают без визуальных артефактов

## Фаза 4: Финальные улучшения (2-3 дня)

### 4.1. Документация критичных компонентов
- [ ] Добавить документацию к публичным API
- [ ] Описать сложные алгоритмы
- [ ] Обновить README проекта
Валидация: Код понятен новому разработчику

### 4.2. Улучшение тестового покрытия
- [ ] Добавить тесты для критичных компонентов
- [ ] Обновить существующие тесты
- [ ] Проверить edge cases
Валидация: Все тесты проходят успешно

### 4.3. Финальная проверка
- [ ] Полное тестирование приложения
- [ ] Проверка производительности
- [ ] Валидация доступности
Валидация: Приложение полностью функционально

## Правила выполнения:
1. Каждый шаг должен оставлять приложение в рабочем состоянии
2. Обязательно выполнять валидацию после каждого подэтапа
3. При обнаружении проблем - немедленный откат изменений
4. Коммитить изменения после каждого подэтапа
5. Документировать все найденные проблемы и их решения

Я помогу найти кастомные элементы стилизации в проекте. Давайте проведем поиск по соответствующим файлам и компонентам.Давайте проверим конкретные файлы, где обычно содержатся модификаторы и стили.Я нашел несколько кастомных элементов стилизации. Давайте проверим еще файл с модификаторами представлений.Я нашел несколько кастомных элементов стилизации, которые можно упростить согласно плану рефакторинга:

1. В файле TouchTargetModifiers.swift:
- Кастомные модификаторы MinimumTouchTargetModifier и ExpandedTouchTargetModifier
- Кастомная анимация withSpringAnimation с константами

2. В файле ViewModifiers.swift:
- Множество кастомных констант для layout в различных структурах (CalendarLayout, ListLayout, BadgeLayout и др.)
- Кастомный модификатор withStandardSpacing
- Кастомные цвета для приоритетов в PriorityColors

Давайте проверим еще, нет ли у нас других файлов с кастомной стилизацией.На основе проведенного анализа, я обнаружил следующие места, требующие упрощения согласно плану рефакторинга:

1. Кастомные тач-таргеты:
   - MinimumTouchTargetModifier и ExpandedTouchTargetModifier можно заменить на стандартные SwiftUI `.contentShape(Rectangle())` и `.frame(minWidth:minHeight:)`
   - Константа SettingsLayout.minTouchTargetSize (44) является стандартной для iOS и может быть захардкожена

2. Кастомные отступы и размеры:
   - Множество структур с константами (CalendarLayout, ListLayout, BadgeLayout и др.) используют сложные вычисления на основе размера шрифта
   - Модификатор withStandardSpacing можно заменить на стандартные стеки SwiftUI с системными отступами

Предлагаю начать с самого простого изменения - упрощения системы отступов, как указано в плане рефакторинга. Хотите, чтобы я подготовил конкретные изменения для какого-то из этих компонентов?